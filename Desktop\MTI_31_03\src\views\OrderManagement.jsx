import React, { useState, useRef, useEffect, useMemo } from "react";
import "../styles.css";
import {
  OptimizedMarketIndex,
  OptimizedTopNav,
  OptimizedErrorContainer,
  OptimizedLeftNav,
  OptimizedRightNav,
} from "../components/Layout/OptimizedComponenets";
import { useSelector, useDispatch } from "react-redux";
import { setAllSeq } from "../store/slices/colSeq";
import { setAllVis } from "../store/slices/colVis";
import PlayArrowIcon from "@mui/icons-material/PlayArrow";
import useClickOutside from "../hooks/useClickOutside";
import TableHeaderWithFilter from "../components/TableHeaderWithFilter";
import filterIcon from "../assets/newFilter.png";

const styles = `
  .middle-main-container {
    flex: 1;
    display: flex;
    flex-direction: column;
  }
  .main-table {
    flex: 1;
    overflow: auto;
    height: calc(92vh - 100px);
    position: relative;
  }
  .orderflowtable {
    width: 100%;
    border-collapse: separate;
    border-spacing: 0;
  }
  .orderflowtable thead {
    position: sticky;
    top: 0;
    z-index: 10;
    background-color: #D8E1FF;
  }
  .orderflowtable th {
    font-size: 15px;
    padding: 4px 3px;
    text-align: center;
    border-bottom: 1px solid #ddd;
    white-space: normal;
    vertical-align: middle;
    height: auto;
    line-height: 1.1;
    writing-mode: horizontal-tb !important;
    text-orientation: mixed !important;
    min-width: 70px; /* Increased minimum width for better appearance */
    word-break: break-word;
    hyphens: auto;
  }
  .orderflowtable td {
    text-align: center;
    vertical-align: middle;
  }
  .orderflowtable tbody tr:nth-child(even),
  .orderflowtable tbody tr:nth-child(even) input,
  .orderflowtable tbody tr:nth-child(even) select {
    background-color: #E8E6E6;
  }
  .orderflowtable tbody tr:nth-child(odd),
  .orderflowtable tbody tr:nth-child(odd) input,
  .orderflowtable tbody tr:nth-child(odd) select {
    background-color: #FFFFFF;
  }
  .filter-icon {
    margin-left: 2px;
    cursor: pointer;
    font-size: 16px;
    vertical-align: middle;
  }
  .tooltip-container {
    position: relative;
    display: inline-block;
    margin: 0 2px;
  }
`;


function OrderManagement() {
  const errorContainerRef = useRef(null);
  const dispatch = useDispatch();

  const { collapsed } = useSelector((state) => state.collapseReducer);
  const [ msgs, setMsgs ] = useState([]);
  const handleClearLogs = () => {
    if (msgs.length === 0) return;
    setMsgs([]);
  };

  const allSeqState = useSelector((state) => state.allSeqReducer);
  const allVisState = useSelector((state) => state.allVisReducer);

  const [ orderManagementColVis, setOrderManagementColVis ] = useState(
    allVisState.ordermanagementVis
  );
  const ordermanagmentCols = [
    "Action",
    "User ID",
    "Source Symbol",
    "Request ID",
    "Exchange",
    "Exchange Symbol",
    "LTP",
    "P&L",
    "Product",
    "Entry Order Type",
    "Entry Order ID",
    "Entry Time",
    "Entry Txn",
    "Entry Qty",
    "Entry Filled Qty",
    "Entry Exchange Time",
    "LTP#1",
    "LTP#2",
    "Entry Avg Price",
    "LTP#3",
    "Entry Status",
    "Exit Order ID",
    "Exit Time",
    "Exit Txn",
    "Exit Qty",
    "Exit Filled Qty",
    "LTP#4",
    "Exit Exchange Time",
    "Exit Avg Price",
    "Exit Status",
    "Target",
    "SL",
    "Break Even",
    "Signal Source",
    "Strategy",
    "Signal Status",
    "Order Failed",
    "User Alies",
    "Remarks",
    "Manual Exit",
  ];

  const [ ordermanagmentColsSelectedALL, setordermanagmentColsSelectedALL ] =
    useState(false);

  const ordermanagmentColSelectALL = () => {
    const newValue = !ordermanagmentColsSelectedALL;
    setordermanagmentColsSelectedALL(newValue);

    const updatedVisibility = {};
    ordermanagmentCols.forEach(col => {
      updatedVisibility[ col ] = !newValue;
    });

    setOrderManagementColVis(updatedVisibility);

    if (!newValue) {
      setordermanagementSeq(ordermanagmentCols);
    } else {
      setordermanagementSeq([]);
    }
  };

  const [ ordermanagementSeq, setordermanagementSeq ] = useState(
    allSeqState.ordermanagementSeq
  );

  useEffect(() => {
    setordermanagementSeq(allSeqState.ordermanagementSeq);
    setOrderManagementColVis(() => {
      const initialVisibility = {};
      ordermanagmentCols.forEach(col => {
        initialVisibility[ col ] = true;
      });
      return initialVisibility;
    });
  }, []);

  useEffect(() => {
    dispatch(
      setAllVis({
        ...allVisState,
        ordermanagementVis: orderManagementColVis,
      })
    );
  }, [ orderManagementColVis ]);

  useEffect(() => {
    dispatch(
      setAllSeq({
        ...allSeqState,
        ordermanagementSeq: ordermanagementSeq,
      })
    );
  }, [ ordermanagementSeq ]);

  const [ orderBook, setOrderBook ] = useState([
    {
      ch: "",
      chp: "",
      clientId: "",
      description: "",
      discloseQty: "",
      disclosedQty: "",
      dqQtyRem: "",
      ex_sym: "",
      exchOrdId: "",
      exchange: "",
      filledQty: "",
      fyToken: "",
      id: "",
      instrument: "",
      limitPrice: "",
      lp: "",
      message: "",
      offlineOrder: "",
      orderDateTime: "",
      orderNumStatus: "",
      orderValidity: "",
      pan: "",
      productType: "",
      qty: "",
      remainingQuantity: "",
      segment: "",
      side: "",
      slNo: "",
      source: "",
      status: "",
      stopPrice: "",
      symbol: "",
      tradedPrice: "",
      type: "",
    },
  ]);

  useEffect(() => {
    if (orderBook.length === 0) {
      setOrderBook([
        {
          ch: "",
          chp: "",
          clientId: "",
          description: "",
          discloseQty: "",
          disclosedQty: "",
          dqQtyRem: "",
          ex_sym: "",
          exchOrdId: "",
          exchange: "",
          filledQty: "",
          fyToken: "",
          id: "",
          instrument: "",
          limitPrice: "",
          lp: "",
          message: "",
          offlineOrder: "",
          orderDateTime: "",
          orderNumStatus: "",
          orderValidity: "",
          pan: "",
          productType: "",
          qty: "",
          remainingQuantity: "",
          segment: "",
          side: "",
          slNo: "",
          source: "",
          status: "",
          stopPrice: "",
          symbol: "",
          tradedPrice: "",
          type: "",
        },
      ]);
    }
  }, [ orderBook ]);

  const mappedOrderBook = useMemo(() => {
    return orderBook.map((order) => ({
      Action: "",
      "User ID": order.clientId,
      "Source Symbol": order.symbol,
      "Request ID": order.id,
      Exchange: order.exchange === "10" ? "NSE" : order.exchange === "11" ? "MCX" : order.exchange === "12" ? "BSE" : "",
      "Exchange Symbol": order.ex_sym,
      LTP: order.lp,
      "P&L": "", // Add actual P&L calculation if available
      Product: order.productType,
      "Entry Order Type": order.type,
      "Entry Order ID": order.exchOrdId,
      "Entry Time": order.orderDateTime,
      "Entry Txn": order.orderNumStatus,
      "Entry Qty": order.qty,
      "Entry Filled Qty": order.filledQty,
      "Entry Exchange Time": "", // Add if available
      "LTP#1": "", // Add if available
      "LTP#2": "", // Add if available
      "Entry Avg Price": order.tradedPrice,
      "LTP#3": "", // Add if available
      "Entry Status": order.status,
      "Exit Order ID": "", // Add if available
      "Exit Time": "", // Add if available
      "Exit Txn": "", // Add if available
      "Exit Qty": "", // Add if available
      "Exit Filled Qty": "", // Add if available
      "LTP#4": "", // Add if available
      "Exit Exchange Time": "", // Add if available
      "Exit Avg Price": "", // Add if available
      "Exit Status": "", // Add if available
      Target: order.limitPrice,
      SL: order.stopPrice,
      "Break Even": "", // Add if available
      "Signal Source": order.source,
      Strategy: "", // Add if available
      "Signal Status": "", // Add if available
      "Order Failed": order.message,
      "User Alies": order.pan,
      Remarks: "", // Add if available
      "Manual Exit": "", // Add if available
    }));
  }, [ orderBook ]);

  const filterPopupRef = useRef(null);
  useClickOutside(filterPopupRef, () => setFilterPopup(null));

  const [ filters, setFilters ] = useState({});
  const [ filterPopup, setFilterPopup ] = useState(null);
  const [ tempFilters, setTempFilters ] = useState({});
  const [ filteredData, setFilteredData ] = useState(mappedOrderBook);
  const [ popupPosition, setPopupPosition ] = useState({ top: 0, left: 0 });
  const [ firstFilteredColumn, setFirstFilteredColumn ] = useState(null);

  useEffect(() => {
    if (Object.keys(filters).length > 0) {
      const filteredDataResult = mappedOrderBook.filter((row) =>
        Object.keys(filters).every((col) =>
          filters[ col ]?.length > 0 ? filters[ col ].includes(row[ col ]) : true
        )
      );
      setFilteredData(filteredDataResult);
    } else {
      setFilteredData(mappedOrderBook);
    }
  }, [ mappedOrderBook, filters ]);

  const handleApplyFilter = () => {
    const newFilters = { ...filters, ...tempFilters };
    if (!firstFilteredColumn) {
      setFirstFilteredColumn(filterPopup);
    }
    setFilters(newFilters);
    setFilterPopup(null);
  };

  const getDynamicUniqueValues = (column) => {
    if (!firstFilteredColumn || column === firstFilteredColumn) {
      return Array.from(new Set(mappedOrderBook.map((row) => row[ column ])));
    }
    return Array.from(new Set(filteredData.map((row) => row[ column ])));
  };

  const handleFilterToggle = (column, event) => {
    const { top, left, height } = event.target.getBoundingClientRect();
    setFilterPopup(filterPopup === column ? null : column);
    setPopupPosition({ top: top + height, left });
    setTempFilters(filters);
  };

  const handleFilterChange = (column, value) => {
    setTempFilters((prev) => {
      const columnFilters = prev[ column ] || [];
      if (columnFilters.includes(value)) {
        return { ...prev, [ column ]: columnFilters.filter((v) => v !== value) };
      } else {
        return { ...prev, [ column ]: [ ...columnFilters, value ] };
      }
    });
  };

  const handleSelectAll = (column) => {
    const currentOptions = getDynamicUniqueValues(column);
    const selectedOptions = tempFilters[ column ] || [];
    const allSelected = currentOptions.every((opt) => selectedOptions.includes(opt));

    if (allSelected) {
      setTempFilters((prev) => ({ ...prev, [ column ]: [] }));
    } else {
      setTempFilters((prev) => ({ ...prev, [ column ]: [ ...currentOptions ] }));
    }
  };

  const handleCancelFilter = () => {
    setTempFilters(filters);
    setFilterPopup(null);
  };

  const hasColumnData = (row, column) => {
    return row[ column ] !== undefined && row[ column ] !== "";
  };

  return (
    <div>
      <style>{styles}</style>
      <OptimizedMarketIndex />
      <div className="main-section">
        <OptimizedLeftNav />
        <div className="middle-main-container">
          <OptimizedTopNav
            pageCols={ordermanagmentCols}
            colVis={orderManagementColVis}
            setColVis={setOrderManagementColVis}
            colsSelectedAll={ordermanagmentColsSelectedALL}
            setColsSelectedALL={setordermanagmentColsSelectedALL}
            selectAll={ordermanagmentColSelectALL}
            setSeq={setordermanagementSeq}
          />
          <div className="main-table">
            <table className="orderflowtable">
              <thead>
                <tr>
                  {ordermanagementSeq.map((column) => {
                    const hasFilter = filters[ column ]?.length > 0;
                    const selectedItems = filters[ column ]?.length || 0;

                    return (
                      <th
                        key={column}
                        style={{
                          fontSize: "13px",
                          padding: "4px 3px",
                          textAlign: "center",
                          backgroundColor: hasFilter ? "#f0f7ff" : "inherit",
                          borderBottom: hasFilter ? "2px solid #1976d2" : "inherit",
                          height: "auto",
                          minWidth: column === "P&L" ? "90px" :
                            column === "Entry Avg Price" ? "120px" :
                              column === "Exit Avg Price" ? "110px" :
                                "150px",
                          wordBreak: "break-word",
                          whiteSpace: "normal"
                        }}
                      >
                        <div style={{
                          display: "flex",
                          flexDirection: "row",
                          alignItems: "center",
                          justifyContent: "center",
                          position: "relative",
                          padding: "0",
                          margin: "0",
                          gap: "4px",
                          width: "100%"
                        }}>
                          <TableHeaderWithFilter
                            col={column}
                            columnDisplayNames={{}}
                            hasFilter={hasFilter}
                            selectedItems={selectedItems}
                            handleFilterToggle={(e) => handleFilterToggle(column, e)}
                            filterIcon={filterIcon}
                          />
                        </div>
                      </th>
                    );
                  })}
                </tr>
              </thead>
              <tbody className="tabletbody">
                {filteredData.map((order, rowIndex) => (
                  <tr
                    key={rowIndex}
                    style={{
                      backgroundColor: rowIndex % 2 === 0 ? "#FFFFFF" : "#E8E6E6"
                    }}
                  >
                    {ordermanagementSeq.map((column) =>
                      hasColumnData(order, column) ? (
                        <td
                          style={{
                            padding: "4px",
                            width: "auto",
                            color:
                              column === "P&L" && parseFloat(order[ column ]) > 0
                                ? "green"
                                : column === "P&L" && parseFloat(order[ column ]) < 0
                                  ? "red"
                                  : "black",
                            verticalAlign: "middle"
                          }}
                          key={column}
                        >
                          {column === "Action" ? (
                            <input
                              type="text"
                              style={{ padding: "4px", border: "none", textAlign: "center" }}
                            />
                          ) : (
                            order[ column ]
                          )}
                        </td>
                      ) : (
                        <td
                          style={{ padding: "4px", width: "auto", verticalAlign: "middle" }}
                          key={column}
                        >
                          {column === "Action" && (
                            <input
                              type="text"
                              style={{ padding: "4px", border: "none", textAlign: "center" }}
                            />
                          )}
                        </td>
                      )
                    )}
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
          <div className="add_collapse">
            <button className="hiddenbutton button">Add</button>
            <button
              style={{ zIndex: "0" }}
              onClick={() => {
                errorContainerRef.current.toggleCollapse();
              }}
              className="button"
              id="collapse"
            >
              {collapsed ? "Expand" : "Collapse"}
            </button>
          </div>
          <OptimizedErrorContainer
            ref={errorContainerRef}
            msgs={msgs}
            handleClearLogs={handleClearLogs}
          />
        </div>
        <OptimizedRightNav />
      </div>
      {filterPopup && filterPopup !== "Action" && (
        <div
          ref={filterPopupRef}
          style={{
            position: "absolute",
            top: `${popupPosition.top + 5}px`,
            left: `${popupPosition.left}px`,
            background: "#ffffff",
            border: "1px solid #e0e0e0",
            borderRadius: "8px",
            padding: "10px",
            boxShadow: "0 2px 4px rgba(0, 0, 0, 0.1)",
            zIndex: 1000,
            minWidth: "150px",
          }}
        >
          <div>
            <label
              style={{
                display: "flex",
                alignItems: "center",
                padding: "2px 5px",
                cursor: "pointer",
                fontWeight: "500",
                color: "#333",
                marginBottom: "0",
              }}
            >
              <input
                type="checkbox"
                checked={
                  tempFilters[ filterPopup ] &&
                  getDynamicUniqueValues(filterPopup).every((opt) =>
                    tempFilters[ filterPopup ].includes(opt)
                  )
                }
                onChange={() => handleSelectAll(filterPopup)}
                style={{
                  marginRight: "8px",
                }}
              />
              <span>Select All</span>
            </label>
            <div
              style={{
                maxHeight: "120px",
                overflowY: "auto",
                margin: "0",
                scrollbarWidth: "thin",
                scrollbarColor: "#888 #f1f1f1",
              }}
            >
              {getDynamicUniqueValues(filterPopup).length > 0 ? (
                getDynamicUniqueValues(filterPopup).map((item) => (
                  <div
                    key={item}
                    style={{
                      display: "flex",
                      alignItems: "center",
                      padding: "1px 5px",
                      cursor: "pointer",
                      margin: "0",
                    }}
                  >
                    <input
                      type="checkbox"
                      checked={tempFilters[ filterPopup ]?.includes(item) || false}
                      onChange={() => handleFilterChange(filterPopup, item)}
                      style={{
                        marginRight: "8px",
                      }}
                    />
                    <span style={{ color: "#444" }}>{item}</span>
                  </div>
                ))
              ) : (
                <div
                  style={{
                    display: "flex",
                    alignItems: "center",
                    padding: "2px 5px",
                    color: "#666",
                    fontStyle: "italic",
                    minHeight: "30px",
                  }}
                >
                  <span>No options available</span>
                </div>
              )}
            </div>
          </div>
          <div
            style={{
              marginTop: "15px",
              display: "flex",
              gap: "10px",
              justifyContent: "flex-end",
            }}
          >
            <button
              onClick={handleCancelFilter}
              style={{
                padding: "6px 12px",
                border: "1px solid #dc3545",
                borderRadius: "4px",
                background: "#dc3545",
                cursor: "pointer",
                color: "white",
                transition: "all 0.2s",
              }}
            >
              Cancel
            </button>
            <button
              onClick={handleApplyFilter}
              style={{
                padding: "6px 12px",
                border: "none",
                borderRadius: "4px",
                background: "#007bff",
                color: "white",
                cursor: "pointer",
                transition: "all 0.2s",
              }}
            >
              OK
            </button>
          </div>
        </div>
      )}
    </div>
  );
}

export default OrderManagement;