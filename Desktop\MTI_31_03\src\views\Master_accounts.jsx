import React, { useState, useRef, useEffect, useCallback } from "react";
import {
  OptimizedMarketIndex,
  OptimizedLeftNav,
  OptimizedRightNav,
  OptimizedErrorContainer,
  OptimizedTopNav,
} from "../components/Layout/OptimizedComponenets";
import { useSelector, useDispatch } from "react-redux";
import Cookies from "universal-cookie";
import Log from "../assets/log.png";
import refreshImg from "../assets/refresh.png";
import MasterChild from "./Master_child";
import _ from "lodash";
import { setConsoleMsgs } from "../store/slices/consoleMsg";
import { setmasterChildAccounts } from "../store/slices/master_child";
import Draggable from "react-draggable";
import { fetchWithAuth } from "../utils/api";
import PlayArrowIcon from "@mui/icons-material/PlayArrow";
import useClickOutside from "../hooks/useClickOutside";
import "../styles/TradePopup.css";

const initialFormData = {
  transactiontype: "",
  producttype: "",
  ordertype: "",
  ordercategory: "",
  exchange: "",
  symbol: "",
  quantity: "",
  duration: "",
  price: "",
  amo: false,
};

function MasterAccount() {
  const [ msgs, setMsgs ] = useState([]);
  const dispatch = useDispatch();
  const cookies = new Cookies();
  const mainUser = cookies.get("USERNAME");

  const handleMsg = (Msg) => {
    dispatch((dispatch, getState) => {
      const previousConsoleMsgs = getState().consoleMsgsReducer.consoleMsgs;
      const lastMsg = previousConsoleMsgs[ 0 ];
      if (
        lastMsg &&
        lastMsg.msg === Msg.msg &&
        lastMsg.user === Msg.user &&
        lastMsg.strategy === Msg.startegy &&
        lastMsg.portfolio === Msg.porttfolio
      ) {
        dispatch(
          setConsoleMsgs({
            consoleMsgs: [ Msg, ...previousConsoleMsgs.slice(1) ],
          })
        );
      } else {
        dispatch(
          setConsoleMsgs({
            consoleMsgs: [ Msg, ...previousConsoleMsgs ],
          })
        );
      }
    });
  };

  const handleClearLogs = () => {
    if (msgs.length === 0) return;
    setMsgs([]);
  };

  const [ selectAll, setSelectAll ] = useState(false);
  const [ mode, setMode ] = useState("create");
  const { collapsed } = useSelector((state) => state.collapseReducer);
  const errorContainerRef = useRef(null);
  const [ isModalOpen, setIsModalOpen ] = useState(false);
  const [ selectedMasterAccount, setSelectedMasterAccount ] = useState(null);
  const [ isTradeModalOpen, setIsTradeModalOpen ] = useState(false);
  const [ selectedExchange, setSelectedExchange ] = useState("NSE");
  const [ selectedAccountId, setSelectedAccountId ] = useState("");
  const [ lotSize, setLotSize ] = useState("1");
  const [ quantityError, setQuantityError ] = useState("");
  const [ popupOpen, setPopupOpen ] = useState(false);
  const [ selectedItems, setSelectedItems ] = useState([]);
  const [ remainingAccounts, setRemainingAccounts ] = useState([]);
  const [ deleteModal, setDeleteModal ] = useState(false);
  const [ sqOffModal, setSqoffModal ] = useState(false);
  const [ errorMessage, setErrorMessage ] = useState("");
  const [ symbols, setSymbols ] = useState([]);
  const [ filteredSymbols, setFilteredSymbols ] = useState([]);
  const [ showDropdown, setShowDropdown ] = useState(false);
  const [ showLotSize, setShowLotSize ] = useState(false);
  const [ showTooltip, setShowTooltip ] = useState(false);
  const [ expandedRowIndex, setExpandedRowIndex ] = useState(null);
  const [ selectedRow, setSelectedRow ] = useState(null);
  const [ positionOq, setPositionOq ] = useState({ x: 0, y: 0 });
  const [ isLoadingSymbols, setIsLoadingSymbols ] = useState(false);
  const [ selectedSymbolIndex, setSelectedSymbolIndex ] = useState(-1);
  const [ symbolLTP, setSymbolLTP ] = useState(null);
  const [ isLoadingLTP, setIsLoadingLTP ] = useState(false);
  const [ priceWarning, setPriceWarning ] = useState("");
  const symbolInputRef = useRef(null);

  const { brokers: rows } = useSelector((state) => state.brokerReducer);
  const { masterChildAccounts: masterChildAccount } = useSelector(
    (state) => state.masterChildAccountsReducer
  );
  const { masterChildPnL } = useSelector((state) => state.masterChildReducer);
  const { placeOrderStart } = useSelector(
    (state) => state.placeOrderStartReducer
  );
  const { positions } = useSelector((state) => state.positionReducer);
  const [ data, setData ] = useState(masterChildAccount);
  const [ masterChildAccounts, setMasterChildAccounts ] = useState([]);
  const [ checkboxes, setCheckboxes ] = useState([]);
  const [ formData, setFormData ] = useState(initialFormData);

  useEffect(() => {
    const sortedData = [ ...masterChildAccount ].sort((a, b) => a.id - b.id);
    setData(sortedData);
    setCheckboxes(sortedData.map(() => false));
  }, [ masterChildAccount ]);

  const [ isBrokerLogin, setIsBrokerLogin ] = useState(false);

  const memoizedValues = React.useMemo(() => {
    const filteredRows = rows
      .filter((row) => row.inputDisabled)
      .filter((row) => {
        const isNotMaster = !data?.some(
          (account) => account.broker_user_id === row.userId
        );
        const isNotChild = !data?.some((account) =>
          account.child_accounts.some(
            (child) => (child.broker_user_id || child.userId) === row.userId
          )
        );
        return isNotMaster && isNotChild;
      });

    const brokerLoginStatus =
      rows.filter((row) => row.inputDisabled).length > 1;

    return { filteredRows, brokerLoginStatus };
  }, [ rows, data ]);

  useEffect(() => {
    setMasterChildAccounts(memoizedValues.filteredRows);
    setIsBrokerLogin(memoizedValues.brokerLoginStatus);
  }, [ memoizedValues ]);

  const fetchMasterAccounts = async () => {
    try {
      const response = await fetchWithAuth(
        `/api/fetch_master_child_accounts/${mainUser}`,
        {
          method: "GET",
        }
      );
      if (!response.ok) {
        throw new Error("Failed to fetch master accounts");
      }
      const MasterChildAccounts = await response.json();
      dispatch(
        setmasterChildAccounts({
          masterChildAccounts: MasterChildAccounts,
        })
      );
    } catch (error) {
      console.error("Error fetching master accounts:", error.message);
    }
  };

  useEffect(() => {
    fetchMasterAccounts();
  }, []);

  const handleSelectAll = () => {
    const allChecked =
      checkboxes.length > 0 && checkboxes.every((checkbox) => checkbox);
    const updatedCheckboxes = data.map(() => !allChecked);
    setCheckboxes(updatedCheckboxes);
    setSelectAll(!allChecked);
  };

  const handleCheckboxChange = (index) => {
    const updatedCheckboxes = [ ...checkboxes ];
    updatedCheckboxes[ index ] = !updatedCheckboxes[ index ];
    setCheckboxes(updatedCheckboxes);
    // Only set selectAll to true if all checkboxes are checked and there's at least one checkbox
    setSelectAll(
      updatedCheckboxes.length > 0 &&
      updatedCheckboxes.every((checkbox) => checkbox)
    );
  };

  const openModalDelete = () => {
    const hasSelectedItems = checkboxes.some((checkbox) => checkbox);

    if (!hasSelectedItems) {
      handleMsg({
        msg: "Please select at least one account to delete.",
        logType: "WARNING",
        timestamp: `${new Date().toLocaleString()}`,
      });
      return;
    }

    setDeleteModal(true);
  };

  const closeModalDelete = () => {
    setDeleteModal(false);
    setErrorMessage("");
  };

  const openModalSqOff = () => {
    const hasSelectedItems = checkboxes.some((checkbox) => checkbox);

    if (!hasSelectedItems) {
      handleMsg({
        msg: "Please select at least one account to square off.",
        logType: "WARNING",
        timestamp: `${new Date().toLocaleString()}`,
      });
      return;
    }

    setSqoffModal(true);
  };

  const closeModalSqOff = () => {
    setSqoffModal(false);
    setErrorMessage("");
  };

  const handleDeleteSelected = async () => {
    const selected = data.filter((_, i) => checkboxes[ i ]);

    try {
      const accountsWithPositions = [];

      for (const item of selected) {
        const masterAccountId = item.broker_user_id;
        const masterHasPositions = positions.some(
          (pos) =>
            pos.broker_user_id === masterAccountId &&
            pos.master_account_id != null &&
            parseInt(pos.netQty) !== 0
        );
        const childAccountIds = item.child_accounts.map(
          (child) => child.broker_user_id || child.userId
        );
        const childrenHavePositions = positions.some(
          (pos) =>
            childAccountIds.includes(pos.broker_user_id) &&
            pos.master_account_id != null &&
            parseInt(pos.netQty) !== 0
        );

        if (masterHasPositions || childrenHavePositions) {
          accountsWithPositions.push(masterAccountId);
        }
      }
      if (accountsWithPositions.length > 0) {
        setDeleteModal(false);
        handleMsg({
          msg: `Cannot delete accounts with open positions: ${accountsWithPositions.join(
            ", "
          )}. Please square off all positions first.`,
          logType: "ERROR",
          timestamp: `${new Date().toLocaleString()}`,
        });
        return;
      }
      await Promise.all(
        selected.map((item) =>
          fetchWithAuth(
            `/api/delete_master_child_accounts/${mainUser}/${item.broker_user_id}`,
            {
              method: "DELETE",
            }
          )
        )
      );

      fetchMasterAccounts();
      setDeleteModal(false);

      handleMsg({
        msg: `Successfully deleted ${selected.length} account(s).`,
        logType: "MESSAGE",
        timestamp: `${new Date().toLocaleString()}`,
      });
    } catch (error) {
      console.error("Error deleting accounts:", error);

      handleMsg({
        msg: `Error deleting accounts: ${error.message}`,
        logType: "ERROR",
        timestamp: `${new Date().toLocaleString()}`,
      });
    }
  };

  const openModal = () => {
    setIsModalOpen(true);
    setData(masterChildAccount);
  };

  const closeModal = () => {
    setIsModalOpen(false);
    setData(masterChildAccount);
  };

  const handleSelectChildAccounts = () => {
    if (selectedMasterAccount) {
      // Filter out the selected master account and any accounts that are already part of other master-child relationships
      const remainingAccounts = masterChildAccounts.filter(
        (account) => account.userId !== selectedMasterAccount.userId
      );
      setRemainingAccounts(remainingAccounts);
    } else {
      setRemainingAccounts([]);
    }
    setMode("create");
    setPopupOpen(true);
    setIsModalOpen(false);
  };

  const handleClosePopup = () => {
    setPopupOpen(false);
  };

  const handleEdit = (selectedRow) => {
    if (!isBrokerLogin) {
      handleMsg({
        msg: "Please login at least one broker account",
        logType: "WARNING",
        timestamp: `${new Date().toLocaleString()}`,
      });
      return;
    }
    const selectedMasterItems = data.filter((_, index) => checkboxes[ index ]);
    const masterAccountId = selectedRow
      ? selectedRow.broker_user_id
      : selectedMasterItems[ 0 ].broker_user_id;
    const remainingAccounts = masterChildAccounts.filter(
      (account) => account.userId !== masterAccountId
    );
    const currentMasterAccount = selectedRow || selectedMasterItems[ 0 ];
    const currentChildAccountIds = currentMasterAccount.child_accounts.map(
      (child) => child.broker_user_id || child.userId
    );
    const childAccountObjects = rows.filter(
      (row) => currentChildAccountIds.includes(row.userId) && row.inputDisabled
    );
    const combinedAccounts = [ ...remainingAccounts, ...childAccountObjects ];

    setSelectedItems(selectedRow ? [ selectedRow ] : selectedMasterItems);
    setRemainingAccounts(combinedAccounts);
    setPopupOpen(true);
    setMode("edit");
  };

  const openTradeModal = () => {
    if (placeOrderStart) {
      setIsTradeModalOpen(true);
      getSymbols(selectedExchange);
    } else {
      handleMsg({
        msg: "To place an Order, Start the Trading.",
        logType: "WARNING",
        timestamp: `${new Date().toLocaleString()}`,
      });
    }
  };

  const closeTradeModal = () => {
    setIsTradeModalOpen(false);
    setFormData(initialFormData);
    setPositionOq({ x: 0, y: 0 });
    setSymbolLTP(null);
    setIsLoadingLTP(false);
    setPriceWarning("");
    setFilteredSymbols([]);
    setShowDropdown(false);
  };

  const handleExchangeChange = (e) => {
    const exchange = e.target.value;
    setFormData((prevState) => ({
      ...prevState,
      exchange: exchange,
      symbol: "",
    }));
    setSelectedExchange(exchange);
    setSymbolLTP(null);
    setPriceWarning("");
    setFilteredSymbols([]);
    setShowDropdown(false);

    if (exchange) {
      getSymbols(exchange);
    }
  };

  const getSymbols = async (selectedExchange) => {
    setIsLoadingSymbols(true);
    setSymbols([]);
    setFilteredSymbols([]);

    const mappedUserIds = rows.filter((row) => row.inputDisabled);
    const angeloneUser = mappedUserIds.find((row) => row.broker === "angelone");
    const clientId = angeloneUser ? angeloneUser.userId : null;

    if (!clientId) {
      console.error("No Angelone broker found in the user data.");
      setIsLoadingSymbols(false);
      return;
    }

    try {
      const response = await fetchWithAuth(
        `/api/angelone_symbols/${mainUser}/${clientId}`,
        {
          method: "POST",
          body: JSON.stringify({
            exchange: selectedExchange,
          }),
        }
      );

      if (!response.ok) {
        throw new Error("Failed to fetch symbols");
      }

      const responseData = await response.json();
      let symbolsData = [];

      if (responseData.angelone_bse_symbols_data?.length > 0) {
        symbolsData = responseData.angelone_bse_symbols_data;
      } else if (responseData.angelone_nse_symbols_data?.length > 0) {
        symbolsData = responseData.angelone_nse_symbols_data;
      } else if (responseData.angelone_nfo_symbols_data?.length > 0) {
        symbolsData = responseData.angelone_nfo_symbols_data;
      }

      symbolsData.sort((a, b) => a.Symbol.localeCompare(b.Symbol));
      setSymbols(symbolsData);
      if (formData.symbol) {
        filterSymbols(formData.symbol);
      }
    } catch (error) {
      console.error("Error fetching symbols:", error.message);
      handleMsg({
        msg: `Error fetching symbols: ${error.message}`,
        logType: "ERROR",
        timestamp: new Date().toLocaleString(),
      });
    } finally {
      setIsLoadingSymbols(false);
    }
  };

  const lotSizeInfo = React.useMemo(() => {
    if (formData.exchange === "NFO" && formData.symbol) {
      const upperCaseSymbol = formData.symbol.toUpperCase();
      if (upperCaseSymbol.startsWith("NIFTY")) {
        return { lotSize: 75, quantity: 75, showLotSize: true };
      } else if (upperCaseSymbol.startsWith("BANKNIFTY")) {
        return { lotSize: 30, quantity: 30, showLotSize: true };
      } else if (upperCaseSymbol.startsWith("FINNIFTY")) {
        return { lotSize: 25, quantity: 25, showLotSize: true };
      } else {
        return { lotSize: 1, quantity: 1, showLotSize: false };
      }
    } else {
      return { lotSize: 1, quantity: 1, showLotSize: false };
    }
  }, [ formData.exchange, formData.symbol ]);

  useEffect(() => {
    setLotSize(lotSizeInfo.lotSize);
    setShowLotSize(lotSizeInfo.showLotSize);
    if (formData.exchange === "NFO" && formData.symbol) {
      setFormData((prev) => ({ ...prev, quantity: lotSizeInfo.quantity }));
    }
  }, [ lotSizeInfo, formData.exchange, formData.symbol ]);

  const handleAccountChange = (event) => {
    setSelectedAccountId(event.target.value);
  };

  const handleTradeSubmit = (e) => {
    e.preventDefault();
    const [ brokerUserId ] = selectedAccountId.split(",");
    const selectedAccount = data?.find(
      (account) => account?.broker_user_id === brokerUserId
    );
    const childAccountSelected = data?.find((account) =>
      account.child_accounts.some((acc) => acc.broker_user_id === brokerUserId)
    );

    if (!selectedAccount && !childAccountSelected) {
      handleMsg({
        msg: "No valid account selected.",
        logType: "ERROR",
        timestamp: new Date().toLocaleString(),
      });
      return;
    }

    const currentTime = new Date();
    const currentHour = String(currentTime.getHours()).padStart(2, "0");
    const currentMinute = String(currentTime.getMinutes()).padStart(2, "0");
    const currentSecond = String(currentTime.getSeconds()).padStart(2, "0");
    const currentFormattedTime = `${currentHour}:${currentMinute}:${currentSecond}`;

    const copyStartTime =
      selectedAccount?.copy_start_time || childAccountSelected?.copy_start_time;
    const copyEndTime =
      selectedAccount?.copy_end_time || childAccountSelected?.copy_end_time;

    const isTradeAllowed =
      (!selectedAccount?.copy_placement &&
        !childAccountSelected?.copy_placement) ||
      ((selectedAccount?.copy_placement ||
        childAccountSelected?.copy_placement) &&
        currentFormattedTime > copyStartTime &&
        currentFormattedTime < copyEndTime);

    if (!isTradeAllowed) {
      const timeRestrictionMessage = `Copy placement is only allowed between ${copyStartTime} and ${copyEndTime}.`;
      handleMsg({
        msg: timeRestrictionMessage,
        logType: "WARNING",
        timestamp: new Date().toLocaleString(),
        user: brokerUserId,
      });
      closeTradeModal();
      return;
    }
    // const currentHours = currentTime.getHours();
    // const currentMinutes = currentTime.getMinutes();

    // if (
    //   !(
    //     (currentHours === 9 && currentMinutes >= 15) ||
    //     (currentHours > 9 && currentHours < 15) ||
    //     (currentHours === 15 && currentMinutes <= 30)
    //   )
    // ) {
    //   console.log(currentHour, currentMinute)
    //   handleMsg({
    //     msg: `Order not placed as current time is outside the allowed time window.`,
    //     logType: "INFO",
    //     timestamp: `${new Date().toLocaleString()}`,
    //   });
    //   closeTradeModal();
    //   return;
    // }


    let accounts = [];
    if (selectedAccount) {
      accounts = [ selectedAccount.broker_user_id ];
      const childAccounts = selectedAccount.child_accounts.map(
        (account) => account.broker_user_id
      );
      accounts = [ ...accounts, ...childAccounts ];
    } else if (childAccountSelected) {
      const selectedChild = childAccountSelected.child_accounts.find(
        (acc) => acc.broker_user_id === brokerUserId
      );
      if (selectedChild) {
        accounts = [ brokerUserId ];
      } else {
        handleMsg({
          msg: "Selected child account not found.",
          logType: "ERROR",
          timestamp: new Date().toLocaleString(),
        });
        return;
      }
    }

    const loggedInAccounts = rows.filter((row) => row.inputDisabled);
    const loggedInUserIds = loggedInAccounts.map((row) => row.userId);
    const missingAccounts = accounts.filter(
      (account) => !loggedInUserIds.includes(account)
    );

    if (missingAccounts.length > 0) {
      const missingAccountsMessage = `Login to ${missingAccounts.join(
        ", "
      )} to place an order.`;
      handleMsg({
        msg: missingAccountsMessage,
        logType: "WARNING",
        timestamp: new Date().toLocaleString(),
      });
      closeTradeModal();
      return;
    }

    if (quantityError) {
      handleMsg({
        msg: quantityError,
        logType: "ERROR",
        timestamp: new Date().toLocaleString(),
      });
      return;
    }

    const updatedFormData = { ...formData };
    if (updatedFormData.ordertype === "MARKET") {
      updatedFormData.price = "0";
    }

    const accountId = selectedAccount?.id || childAccountSelected?.id;
    if (!accountId) {
      handleMsg({
        msg: "Account ID is missing.",
        logType: "ERROR",
        timestamp: new Date().toLocaleString(),
      });
      return;
    }

    const endpoint = selectedAccount
      ? `api/place_master_child_order/${mainUser}/${accountId}`
      : `api/place_master_child_order/${mainUser}/${accountId}?child_broker_user_id=${accounts.join(
        ","
      )}`;

    fetchWithAuth(endpoint, {
      method: "POST",
      body: JSON.stringify(updatedFormData),
    })
      .then((response) => {
        if (!response.ok) {
          throw new Error(`HTTP error! Status: ${response.status}`);
        }
        return response.json();
      })
      .then((data) => {
        setFormData(initialFormData);
        const processResponse = (responses) => {
          responses.forEach((response) => {
            if (!Array.isArray(response) || response.length === 0) return;
            let orderId = "unknown";
            let message = "";
            if (Array.isArray(response[ 0 ])) {
              const [ orderData ] = response;
              if (Array.isArray(orderData) && orderData.length >= 2) {
                orderId = orderData[ 0 ];
                message = `${orderId}: ${orderData[ 1 ]}`;
              } else {
                message = "Unable to process response";
              }
            } else if (response.length >= 2) {
              orderId = response[ 0 ];
              message = `${orderId}: ${response[ 1 ]}`;
            } else {
              message = "Invalid response format";
            }
            handleMsg({
              msg: message,
              logType: "MESSAGE",
              timestamp: new Date().toLocaleString(),
              user: orderId,
            });
          });
        };
        const allResponses = [
          ...(data.master_order_response || []),
          ...(data.child_order_responses || []),
        ];
        processResponse(allResponses);
      })
      .catch((error) => {
        handleMsg({
          msg: `Failed to place trade: ${error.message}`,
          logType: "ERROR",
          timestamp: new Date().toLocaleString(),
          user: brokerUserId,
        });
      })
      .finally(() => {
        closeTradeModal();
      });
  };

  const SqOffSelected = async () => {
    const selectedItems = data.filter((_, index) => checkboxes[ index ]);
    try {
      await Promise.all(
        selectedItems.map(async (item) => {
          const ResponseSqoff = await fetchWithAuth(
            `/api/square_off_master_child/${mainUser}/${item.id}`,
            {
              method: "POST",
            }
          );
          if (!ResponseSqoff.ok) {
            throw new Error(
              `Failed to square off account ${item.broker_user_id}`
            );
          }

          const SqoffResponse = await ResponseSqoff.json();
          if (Array.isArray(SqoffResponse)) {
            SqoffResponse.forEach((responseObj) => {
              handleMsg({
                msg: responseObj.message,
                logType: "MESSAGE",
                timestamp: `${new Date().toLocaleString()}`,
                user: item?.broker_user_id,
              });
            });
          } else {
            handleMsg({
              msg: SqoffResponse.message,
              logType: "MESSAGE",
              timestamp: `${new Date().toLocaleString()}`,
              user: item?.broker_user_id,
            });
          }
        })
      );
      setSqoffModal(false);
    } catch (error) {
      setSqoffModal(false);
      console.error("Error during square off:", error);
      handleMsg({
        msg: `Error during square off: ${error.message}`,
        logType: "ERROR",
        timestamp: `${new Date().toLocaleString()}`,
      });
    }
  };

  const childSqOffSelected = async (userId, row) => {
    const accountId = row.id;
    try {
      const ResponseSqoff = await fetchWithAuth(
        `api/square_off_master_child/${mainUser}/${accountId}?child_broker_user_id=${userId.broker_user_id}`,
        {
          method: "POST",
        }
      );
      if (ResponseSqoff.ok) {
        const SqoffResponse = await ResponseSqoff.json();
        if (Array.isArray(SqoffResponse)) {
          SqoffResponse.forEach((responseObj) => {
            handleMsg({
              msg: responseObj.message,
              logType: "MESSAGE",
              timestamp: `${new Date().toLocaleString()}`,
              user: userId.broker_user_id,
            });
          });
        } else {
          handleMsg({
            msg: SqoffResponse.message,
            logType: "MESSAGE",
            timestamp: `${new Date().toLocaleString()}`,
            user: userId.broker_user_id,
          });
        }
      }
    } catch (error) {
      console.error("Error during child square off:", error);
    }
  };
  const particularPositionsSq = async (userId, row, position) => {
    const accountId = row.id;
    try {
      const ResponseSqoff = await fetchWithAuth(
        `api/square_off_one_mc_position/${mainUser}/${accountId}/${position.symbol}/${userId.broker}/${userId.broker_user_id}`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            product_type: position.product_type,
            order_type: position.order_type,
            transaction_type: position.transaction_type,
          }),
        }
      );
      if (ResponseSqoff.ok) {
        const SqoffResponse = await ResponseSqoff.json();
        if (Array.isArray(SqoffResponse)) {
          SqoffResponse.forEach((responseObj) => {
            handleMsg({
              msg: responseObj.message,
              logType: "MESSAGE",
              timestamp: `${new Date().toLocaleString()}`,
              user: userId.broker_user_id,
            });
          });
        } else {
          handleMsg({
            msg: SqoffResponse.message,
            logType: "MESSAGE",
            timestamp: `${new Date().toLocaleString()}`,
            user: userId.broker_user_id,
          });
        }
      }
    } catch (error) {
      console.error("Error during child square off:", error);
    }
  };

  const masterSqOffSelected = async (row) => {
    const accountId = row.id;
    try {
      const ResponseSqoff = await fetchWithAuth(
        `api/square_off_master_child/${mainUser}/${accountId}`,
        {
          method: "POST",
        }
      );
      if (ResponseSqoff.ok) {
        const SqoffResponse = await ResponseSqoff.json();
        if (Array.isArray(SqoffResponse)) {
          SqoffResponse.forEach((responseObj) => {
            handleMsg({
              msg: responseObj.message,
              logType: "MESSAGE",
              timestamp: `${new Date().toLocaleString()}`,
              user: row.broker_user_id,
            });
          });
        } else {
          handleMsg({
            msg: SqoffResponse.message,
            logType: "MESSAGE",
            timestamp: `${new Date().toLocaleString()}`,
            user: row.broker_user_id,
          });
        }
      }
    } catch (error) {
      console.error("Error during master square off:", error);
    }
  };

  const filterSymbols = (input) => {
    setSelectedSymbolIndex(-1);

    if (!input || !formData.exchange) {
      setFilteredSymbols([]);
      setShowDropdown(false);
      return;
    }

    if (isLoadingSymbols) {
      return;
    }

    const searchTerms = input.toLowerCase().trim().split(/\s+/);
    const monthMappings = {
      jan: "jan",
      january: "jan",
      feb: "feb",
      february: "feb",
      mar: "mar",
      march: "mar",
      apr: "apr",
      april: "apr",
      may: "may",
      jun: "jun",
      june: "jun",
      jul: "jul",
      july: "jul",
      aug: "aug",
      august: "aug",
      sep: "sep",
      september: "sep",
      sept: "sep",
      oct: "oct",
      october: "oct",
      nov: "nov",
      november: "nov",
      dec: "dec",
      december: "dec",
    };

    const contractMappings = {
      fut: "fut",
      future: "fut",
      futures: "fut",
      pe: "pe",
      put: "pe",
      puts: "pe",
      ce: "ce",
      call: "ce",
      calls: "ce",
      opt: "opt",
      option: "opt",
      options: "opt",
    };

    const indexMappings = {
      nifty: "nifty",
      nf: "nifty",
      banknifty: "banknifty",
      bn: "banknifty",
      bank: "banknifty",
      finnifty: "finnifty",
      fn: "finnifty",
      fin: "finnifty",
    };

    const normalizedTerms = searchTerms.map((term) => {
      if (/^(20)?\d{2}$/.test(term)) {
        return term.length === 2 ? term : term.slice(2);
      }

      if (monthMappings[ term ]) return monthMappings[ term ];
      if (contractMappings[ term ]) return contractMappings[ term ];
      if (indexMappings[ term ]) return indexMappings[ term ];
      return term;
    });

    const multiTermMatches = symbols.filter((symbol) => {
      const symbolLower = symbol.Symbol.toLowerCase();

      return normalizedTerms.every((term) => {
        return symbolLower.includes(term);
      });
    });

    const priorityOrder = [ "nifty", "banknifty", "finnifty" ];

    const customSort = (a, b) => {
      const symbolA = a.Symbol.toLowerCase();
      const symbolB = b.Symbol.toLowerCase();

      const exactMatchA = symbolA.includes(input.toLowerCase());
      const exactMatchB = symbolB.includes(input.toLowerCase());
      if (exactMatchA && !exactMatchB) return -1;
      if (!exactMatchA && exactMatchB) return 1;

      const indexA = priorityOrder.findIndex((priority) =>
        symbolA.includes(priority)
      );
      const indexB = priorityOrder.findIndex((priority) =>
        symbolB.includes(priority)
      );
      if (indexA !== -1 && indexB !== -1) {
        if (indexA !== indexB) return indexA - indexB;
      } else if (indexA !== -1) return -1;
      else if (indexB !== -1) return 1;

      const isFutA = symbolA.includes("fut");
      const isFutB = symbolB.includes("fut");
      if (isFutA && !isFutB) return -1;
      if (!isFutA && isFutB) return 1;

      const currentMonth = new Date().getMonth() + 1;
      const monthNames = [
        "jan",
        "feb",
        "mar",
        "apr",
        "may",
        "jun",
        "jul",
        "aug",
        "sep",
        "oct",
        "nov",
        "dec",
      ];
      let monthA = -1;
      let monthB = -1;

      monthNames.forEach((month, index) => {
        if (symbolA.includes(month)) monthA = index + 1;
        if (symbolB.includes(month)) monthB = index + 1;
      });

      const distanceA = monthA !== -1 ? (monthA - currentMonth + 12) % 12 : 999;
      const distanceB = monthB !== -1 ? (monthB - currentMonth + 12) % 12 : 999;

      if (distanceA !== distanceB) return distanceA - distanceB;

      return symbolA.localeCompare(symbolB);
    };

    const sortedFiltered = multiTermMatches.sort(customSort);
    const limitedResults = sortedFiltered.slice(0, 100);

    setFilteredSymbols(limitedResults);
    setShowDropdown(limitedResults.length > 0);
  };

  const debounceFilterSymbols = useCallback(
    _.debounce((input) => {
      filterSymbols(input);
    }, 100),
    [ symbols, isLoadingSymbols ]
  );

  const validateQuantity = (quantity) => {
    // Convert quantity to a number for validation
    const numericQuantity = parseInt(quantity, 10);

    // Check if it's a valid number and a multiple of lot size
    if (isNaN(numericQuantity) || numericQuantity % lotSize !== 0) {
      setQuantityError(`Quantity must be a multiple of ${lotSize}`);
    } else {
      setQuantityError("");
    }

    console.log(
      "Validating quantity:",
      quantity,
      "Numeric:",
      numericQuantity,
      "Lot size:",
      lotSize
    );
  };

  const validatePriceAgainstLTP = () => {
    setPriceWarning("");

    // Get the current full price value directly from the form state
    const fullPrice = formData.price;
    console.log("FINAL VALIDATION - Full price:", fullPrice);

    if (formData.ordertype === "LIMIT" && symbolLTP && fullPrice) {
      // Parse the values
      const price = parseInt(fullPrice, 10);
      const ltp = parseFloat(symbolLTP);

      console.log("FINAL VALIDATION - Parsed price:", price, "LTP:", ltp);

      if (price > ltp && formData.transactiontype === "BUY") {
        setPriceWarning(
          `Warning: Your buy price (${fullPrice}) is more than the current LTP (${ltp})`
        );
      } else if (price < ltp && formData.transactiontype === "SELL") {
        setPriceWarning(
          `Warning: Your sell price (${fullPrice}) is less than the current LTP (${ltp})`
        );
      } else {
        setPriceWarning("");
      }

      // Return validation result to allow callers to know if validation passed
      return (
        (price > ltp && formData.transactiontype === "BUY") ||
        (price < ltp && formData.transactiontype === "SELL")
      );
    }

    return false; // No validation issues if we get here
  };

  const handleInputChange = (e) => {
    const { name, value, type, checked } = e.target;

    if (
      name === "price" ||
      name === "quantity" ||
      name === "triggerPrice" ||
      name === "disclosedQty"
    ) {
      return;
    }

    // Handle checkbox inputs differently
    if (type === "checkbox") {
      setFormData((prevState) => ({
        ...prevState,
        [ name ]: checked,
      }));
      return;
    }

    setFormData((prevState) => ({
      ...prevState,
      [ name ]: value,
    }));

    if (name === "symbol") {
      debounceFilterSymbols(value);
    } else if (name === "ordertype") {
      // Remove automatic LTP fetch on order type change
      if (value !== "LIMIT" && value !== "MARKET") {
        setSymbolLTP(null);
        setPriceWarning("");
      }
    } else if (name === "transactiontype") {
      // Remove automatic LTP fetch on transaction type change
      // Only clear warnings if needed
      if (symbolLTP && formData.price) {
        validatePriceAgainstLTP();
      }
    }
  };

  // Removed automatic LTP fetch on symbol change
  // LTP will only be fetched on symbol selection or refresh button click

  useEffect(() => {
    if (
      symbolLTP &&
      formData.price &&
      (formData.ordertype === "LIMIT" || formData.ordertype === "MARKET")
    ) {
      validatePriceAgainstLTP();
    }
  }, [ symbolLTP, formData.transactiontype, formData.price ]);

  const handleSymbolKeyDown = (e) => {
    if (!showDropdown || filteredSymbols.length === 0) return;

    switch (e.key) {
      case "ArrowDown":
        e.preventDefault(); // Prevent scrolling the page
        setSelectedSymbolIndex((prev) =>
          prev < filteredSymbols.length - 1 ? prev + 1 : prev
        );
        break;

      case "ArrowUp":
        e.preventDefault(); // Prevent scrolling the page
        setSelectedSymbolIndex((prev) => (prev > 0 ? prev - 1 : 0));
        break;

      case "Enter":
        e.preventDefault(); // Prevent form submission
        if (
          selectedSymbolIndex >= 0 &&
          selectedSymbolIndex < filteredSymbols.length
        ) {
          handleSymbolSelect(filteredSymbols[ selectedSymbolIndex ]);
        }
        break;

      case "Escape":
        e.preventDefault();
        setShowDropdown(false);
        break;

      default:
        break;
    }
  };

  const fetchSymbolLTP = async (symbol) => {
    if (!symbol) return;

    setIsLoadingLTP(true);
    setSymbolLTP(null);

    try {
      const mappedUserIds = rows.filter((row) => row.inputDisabled);
      const angeloneUser = mappedUserIds.find(
        (row) => row.broker === "angelone"
      );
      const clientId = angeloneUser ? angeloneUser.userId : null;

      if (!clientId) {
        console.error("No Angelone broker found in the user data.");
        setIsLoadingLTP(false);
        return;
      }
      const requestBody = {
        symbol: symbol,
      };

      const response = await fetchWithAuth(`api/mc_price_details/${mainUser}`, {
        method: "POST",
        body: JSON.stringify(requestBody),
      });

      if (!response.ok) {
        throw new Error("Failed to fetch LTP");
      }

      const data = await response.json();
      const ltp = data.ltp;

      setSymbolLTP(ltp);

      if (
        (formData.ordertype === "LIMIT" || formData.ordertype === "MARKET") &&
        formData.price
      ) {
        validatePriceAgainstLTP();
      }
    } catch (error) {
      console.error("Error fetching LTP:", error.message);
      handleMsg({
        msg: `Error fetching LTP: ${error.message}`,
        logType: "ERROR",
        timestamp: new Date().toLocaleString(),
      });
    } finally {
      setIsLoadingLTP(false);
    }
  };

  // Manual LTP refresh - only called when refresh button is clicked
  const refreshLTP = () => {
    if (formData.symbol) {
      fetchSymbolLTP(formData.symbol);
    } else if (formData.exchange) {
      handleMsg({
        msg: "Please select a symbol first to fetch LTP",
        logType: "WARNING",
        timestamp: new Date().toLocaleString(),
      });
    } else {
      handleMsg({
        msg: "Please select an exchange and symbol first to fetch LTP",
        logType: "WARNING",
        timestamp: new Date().toLocaleString(),
      });
    }
  };

  const handleSymbolSelect = (symbol) => {
    setFormData((prevState) => ({
      ...prevState,
      symbol: symbol.Symbol,
    }));
    setFilteredSymbols([]);
    setShowDropdown(false);
    fetchSymbolLTP(symbol.Symbol);
  };

  const handleMouseEnter = () => {
    setShowTooltip(true);
  };

  const handleMouseLeave = () => {
    setShowTooltip(false);
  };

  const handleExpandRow = (index) => {
    setExpandedRowIndex(expandedRowIndex === index ? null : index);
  };
  const handleExpandPostionsRow = (userId) => {
    setSelectedRow(selectedRow === userId ? null : userId);
  };

  const calculateTotalPnl = (row) => {
    const totalPnl = masterChildPnL[ row.id ]?.total_pnl || 0;
    return {
      totalPnl: totalPnl.toFixed(2),
    };
  };

  const buttonStyle = {
    backgroundColor: "#4661bd",
    color: "#fff",
    border: "none",
    padding: "8px 10px",
    borderRadius: "5px",
    cursor: "pointer",
    marginTop: "0px",
    marginRight: "10px",
  };
  const buttonStyleDelete = {
    ...buttonStyle,
    backgroundColor: "#f44336",
    padding: "8px 10px",
  };
  const buttonStyleTrade = {
    ...buttonStyle,
    backgroundColor: "#FF9800",
    marginLeft: "5px",
  };
  const buttonStyleSqOff = {
    ...buttonStyle,
    backgroundColor: "#4CAF50",
    marginLeft: "5px",
  };
  const buttonsContainerStyle = { display: "flex", marginTop: "-15px" };

  const tableStyle = {};
  const thStyle = {
    backgroundColor: "#e6f0ff",
    color: "#1f2937",
    textAlign: "center",
    position: "sticky",
    top: "0",
    fontSize: "13px",
    padding: "8px 6px",
    fontWeight: "600",
    zIndex: "50",
    // boxSizing: "border-box",
  };
  const thStyleM = {
    backgroundColor: "#e6f0ff",
    color: "#1f2937",
    padding: "6px 8px",
    textAlign: "center",
    position: "sticky",
    top: "0",
    fontSize: "13px",
    fontWeight: "600",
    zIndex: "50",
    // boxSizing: "border-box",
  };
  const tdStyle = {
    padding: "1px",
    textAlign: "center",
    fontSize: "15px",
  };
  const onStyle = { color: "green", fontWeight: "bold" };
  const offStyle = { color: "red", fontWeight: "bold" };
  const modalStyle1 = {
    position: "fixed",
    top: "50%",
    left: "50%",
    transform: "translate(-50%, -50%)",
    backgroundColor: "#fff",
    padding: "20px",
    boxShadow: "0 2px 10px rgba(0,0,0,0.1)",
    zIndex: 1000,
    borderRadius: "5px",
    width: "341px",
    textAlign: "center",
  };
  const overlayStyle = {
    position: "fixed",
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: "rgba(0,0,0,0.3)",
    zIndex: 999,
  };
  const closeButtonStyle = {
    backgroundColor: "#007bff",
    color: "#fff",
    border: "none",
    padding: "10px 20px",
    borderRadius: "5px",
    marginTop: "20px",
    cursor: "pointer",
  };
  const cancelButtonStyle = {
    backgroundColor: "#007bff",
    color: "#fff",
    border: "none",
    padding: "10px 20px",
    borderRadius: "5px",
    cursor: "pointer",
    marginTop: "20px",
    marginLeft: "40px",
  };
  const DeleteButtonStyle = {
    backgroundColor: "#f44336",
    color: "#fff",
    border: "none",
    padding: "10px 20px",
    borderRadius: "5px",
    cursor: "pointer",
    marginTop: "20px",
  };
  const yesButtonStyle = {
    color: "#fff",
    border: "none",
    padding: "10px 20px",
    borderRadius: "5px",
    cursor: "pointer",
    marginTop: "20px",
    backgroundColor: "#4CAF50",
  };
  const noButtonStyle = {
    color: "#fff",
    border: "none",
    padding: "10px 20px",
    borderRadius: "5px",
    cursor: "pointer",
    marginTop: "20px",
    marginLeft: "40px",
    backgroundColor: "#f44336",
  };
  // All styles have been moved to TradePopup.css
  const tooltipTextStyle = {
    visibility: showTooltip ? "visible" : "hidden",
    width: "220px",
    backgroundColor: "black",
    color: "#fff",
    textAlign: "center",
    borderRadius: "6px",
    padding: "5px",
    position: "absolute",
    zIndex: 1,
    bottom: "-70%",
    left: "50%",
    transform: "translateX(-50%)",
    opacity: showTooltip ? 1 : 0,
    transition: "opacity 0.3s",
  };
  const arrowStyle = {
    content: '""',
    position: "absolute",
    top: "-21%",
    left: "50%",
    marginLeft: "-5px",
    borderWidth: "5px",
    borderStyle: "solid",
    borderColor: "transparent transparent black transparent",
  };

  const resetButton = (event) => {
    event.preventDefault();
    setSelectedAccountId("");
    setSymbolLTP(null);
    setPriceWarning("");
    setQuantityError("");
    setFormData(initialFormData);
    setFilteredSymbols([]);
    console.log(initialFormData, "initialFormData");
  };

  const isFormValid = () => {
    if (!formData.exchange) return false;

    if (!formData.symbol) return false;

    if (!formData.quantity) return false;

    if (!formData.transactiontype) return false;

    if (!formData.producttype) return false;

    if (!formData.ordertype) return false;

    if (!formData.ordercategory) return false;

    if (formData.ordertype === "LIMIT" && !formData.price) return false;

    if (
      (formData.ordertype === "STOP_LOSS" ||
        formData.ordertype === "SL_MARKET") &&
      !formData.triggerPrice
    )
      return false;

    if (!selectedAccountId) return false;

    if (priceWarning || quantityError) return false;

    if (formData.ordertype === "LIMIT" && symbolLTP && formData.price) {
      const price = parseInt(formData.price, 10);
      const ltp = parseFloat(symbolLTP);

      if (
        (price > ltp && formData.transactiontype === "BUY") ||
        (price < ltp && formData.transactiontype === "SELL")
      ) {
        return false;
      }
    }

    return true;
  };
  const style = `
  .main-table {
    position: relative;
    overflow: auto;
    height: 92%;
  }
  .table {
    width: 100%;
  }
  .table thead {
    position: sticky;
    top: 0;
    z-index: 10;
    background-color: #f4f4f4;
  }
  .table th {
    font-size: 18px;
    padding: 10px 15px;
    width: 100px;
    text-align: center;
  }
  .table tbody td {
    padding: 10px;
    width: 100px;
    text-align: center;
  }
  @keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }
    .filter-popup {
    position: absolute;
    background: #ffffff;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    padding: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    z-index: 1000;
    min-width: 150px;
    max-width: 200px;
  }
   label {
    display: flex;
    align-items: center;
    padding: 4px;
    cursor: pointer;
  }
   input[type="checkbox"] {
    margin-right: 8px;
  }
   .scrollable-options {
    max-height: 150px;
    overflow-y: auto;
    border: 1px solid #eee;
    border-radius: 4px;
    margin: 4px 0;
  }
  button {
    padding: 6px 12px;
    border-radius: 4px;
    cursor: pointer;
    font-weight: 500;
  }
.cancel-btn {
    border: 1px solid #ccc;
    background: #f8f9fa;
    color: #333;
  }
   .apply-btn {
    border: none;
    background: #1976d2;
    color: white;
  }
`;
  const [ filters, setFilters ] = useState({}); // Applied filters
  const [ tempFilters, setTempFilters ] = useState({}); // Temporary filters for popup
  const [ filterPopup, setFilterPopup ] = useState(null); // Active filter column
  const [ popupPosition, setPopupPosition ] = useState({ top: 0, left: 0 }); // Popup position
  const [ filteredData, setFilteredData ] = useState(data); // Filtered table data
  const filterPopupRef = useRef(null);

  useClickOutside(filterPopupRef, () => setFilterPopup(null));

  // Update filteredData when data or filters change
  useEffect(() => {
    if (Object.keys(filters).length > 0) {
      const filteredDataResult = data.filter((row) =>
        Object.keys(filters).every((col) =>
          filters[ col ]?.length > 0 ? filters[ col ].includes(row[ col ]) : true
        )
      );
      setFilteredData(filteredDataResult);
    } else {
      setFilteredData(data);
    }
  }, [ data, filters ]);

  // Get unique values for the Name column
  const getDynamicUniqueValues = (column) => {
    return Array.from(new Set(data.map((row) => row[ column ] || "")));
  };

  // Toggle filter popup
  const handleFilterToggle = (column, event) => {
    const { top, left, height } = event.target.getBoundingClientRect();
    setFilterPopup(filterPopup === column ? null : column);
    setPopupPosition({ top: top + height, left });
    setTempFilters(filters);
  };

  // Handle filter value selection
  const handleFilterChange = (column, value) => {
    setTempFilters((prev) => {
      const columnFilters = prev[ column ] || [];
      if (columnFilters.includes(value)) {
        return { ...prev, [ column ]: columnFilters.filter((v) => v !== value) };
      } else {
        return { ...prev, [ column ]: [ ...columnFilters, value ] };
      }
    });
  };

  // Handle Select All
  const handleSelectAllFilters = (column) => {
    const currentOptions = getDynamicUniqueValues(column);
    const selectedOptions = tempFilters[ column ] || [];
    const allSelected = currentOptions.every((opt) =>
      selectedOptions.includes(opt)
    );

    if (allSelected) {
      setTempFilters((prev) => ({ ...prev, [ column ]: [] }));
    } else {
      setTempFilters((prev) => ({ ...prev, [ column ]: [ ...currentOptions ] }));
    }
  };

  // Apply filters
  const handleApplyFilter = () => {
    const newFilters = {
      ...filters,
      [ filterPopup ]: tempFilters[ filterPopup ] || [],
    };
    setFilters(newFilters);
    setFilterPopup(null);
  };

  // Cancel filter changes
  const handleCancelFilter = () => {
    setTempFilters(filters);
    setFilterPopup(null);
  };

  return (
    <div>
      <style>{style}</style>
      <OptimizedMarketIndex />
      <div className="main-section">
        <OptimizedLeftNav />
        <div className="middle-main-container">
          <OptimizedTopNav />
          <div style={buttonsContainerStyle}>
            <button style={buttonStyle} onClick={openModal}>
              Create
            </button>
            <button style={buttonStyleDelete} onClick={openModalDelete}>
              Delete
            </button>
            <button style={buttonStyleTrade} onClick={openTradeModal}>
              Trade
            </button>
            <button style={buttonStyleSqOff} onClick={openModalSqOff}>
              Sq off
            </button>
          </div>
          <div className="main-table" style={{ marginTop: "-10px" }}>
            <table
              style={{
                ...tableStyle,
                boxShadow: "0 4px 12px rgba(0,0,0,0.1)",
                borderRadius: "8px",
                overflow: "visible",
                width: "100%",
                tableLayout: "fixed",
              }}
            >
              <thead
                style={{
                  position: "sticky",
                  top: "0",
                  zIndex: "50",
                  backgroundColor: "#e6f0ff",
                  width: "100%",
                }}
              >
                <tr style={{ fontSize: "13px", fontWeight: "600" }}>
                  <th
                    style={{
                      ...thStyle,
                      width: "40px",
                      padding: "8px 6px",
                      color: "#1f2937",
                      textAlign: "center",
                    }}
                  >
                    <input
                      type="checkbox"
                      checked={
                        checkboxes.length > 0 && checkboxes.every(Boolean)
                      }
                      onChange={handleSelectAll}
                      style={{
                        width: "18px",
                        height: "18px",
                        cursor: "pointer",
                      }}
                    />
                  </th>

                  <th
                    style={{
                      ...thStyle,
                      width: "100px",
                      padding: "8px 6px",
                      color: "#1f2937",
                      textAlign: "center",
                    }}
                  >
                    <div
                      style={{
                        display: "flex",
                        alignItems: "center",
                        justifyContent: "center",
                      }}
                    >
                      <span>Name</span>
                      <span
                        onClick={(e) => handleFilterToggle("name", e)}
                        style={{
                          marginLeft: 8,
                          cursor: "pointer",
                          fontSize: "14px",
                          color:
                            filters[ "name" ]?.length > 0 ? "#1976d2" : "black",
                        }}
                      >
                        <PlayArrowIcon
                          style={{
                            transform: "rotate(90deg)",
                            fontSize: "14px",
                            color:
                              filters[ "name" ]?.length > 0 ? "#1976d2" : "black",
                          }}
                        />
                      </span>
                    </div>
                  </th>
                  <th
                    style={{
                      ...thStyle,
                      width: "90px",
                      padding: "8px 6px",
                      color: "#1f2937",
                      textAlign: "center",
                    }}
                  >
                    Client ID
                  </th>
                  <th
                    style={{
                      ...thStyle,
                      width: "100px",
                      padding: "8px 6px",
                      color: "#1f2937",
                      textAlign: "center",
                    }}
                  >
                    Broker
                  </th>
                  <th
                    style={{
                      ...thStyle,
                      width: "60px",
                      padding: "8px 6px",
                      color: "#1f2937",
                      textAlign: "center",
                    }}
                  >
                    P&L
                  </th>
                  <th
                    style={{
                      ...thStyle,
                      width: "60px",
                      padding: "8px 6px",
                      color: "#1f2937",
                      textAlign: "center",
                    }}
                  >
                    Sq Off
                  </th>
                  <th
                    style={{
                      ...thStyle,
                      width: "60px",
                      padding: "8px 6px",
                      color: "#1f2937",
                      textAlign: "center",
                    }}
                  >
                    Live
                  </th>
                  <th
                    style={{
                      ...thStyle,
                      width: "70px",
                      padding: "8px 6px",
                      color: "#1f2937",
                      textAlign: "center",
                    }}
                  >
                    Copy-Place
                  </th>
                  <th
                    style={{
                      ...thStyle,
                      width: "70px",
                      padding: "8px 6px",
                      color: "#1f2937",
                      textAlign: "center",
                    }}
                  >
                    Copy-Cancel
                  </th>
                  <th
                    style={{
                      ...thStyle,
                      width: "70px",
                      padding: "8px 6px",
                      color: "#1f2937",
                      textAlign: "center",
                    }}
                  >
                    Copy-Modify
                  </th>
                  <th
                    style={{
                      ...thStyle,
                      width: "70px",
                      padding: "8px 6px",
                      color: "#1f2937",
                      textAlign: "center",
                    }}
                  >
                    Child accounts
                  </th>
                  <th
                    style={{
                      ...thStyle,
                      width: "70px",
                      padding: "8px 6px",
                      color: "#1f2937",
                      textAlign: "center",
                    }}
                  >
                    Total Multiplier
                  </th>
                </tr>
              </thead>
              <tbody>
                {filteredData?.map((row, index) => {
                  const { totalPnl } = calculateTotalPnl(row);
                  return (
                    <React.Fragment key={index}>
                      <tr
                        style={{
                          backgroundColor:
                            index % 2 === 0 ? "#ffffff" : "#f9fafb",
                          transition: "background-color 0.2s",
                          "&:hover": {
                            backgroundColor: "#f0f9ff",
                          },
                        }}
                      >
                        <td
                          style={{
                            ...tdStyle,
                            padding: "6px",
                            fontSize: "15px",
                            fontWeight: "bold",
                            cursor: "pointer",
                            textAlign: "center",
                            display: "flex",
                            alignItems: "center",
                            justifyContent: "center",
                            gap: "8px",
                          }}
                        >
                          <span
                            onClick={() => handleExpandRow(index)}
                            style={{
                              display: "inline-flex",
                              alignItems: "center",
                              justifyContent: "center",
                              width: "20px",
                              height: "20px",
                              backgroundColor: "#e6f7ff",
                              color: "#1890ff",
                              cursor: "pointer",
                              transition: "all 0.3s",
                              fontSize: "14px",
                              marginTop: "5px",
                            }}
                          >
                            {expandedRowIndex === index ? "−" : "+"}
                          </span>
                          <input
                            type="checkbox"
                            checked={checkboxes[ index ] || false}
                            onChange={() => handleCheckboxChange(index)}
                            style={{
                              width: "16px",
                              height: "16px",
                              cursor: "pointer",
                              marginTop: "5px",
                            }}
                          />
                        </td>
                        <td
                          style={{
                            alignItems: "center",
                            justifyContent: "center",
                            padding: "6px",
                            textAlign: "center",
                            cursor: "pointer",
                            transition: "all 0.3s",
                            fontSize: "17px",
                          }}
                        >
                          <span
                            style={{
                              color: "#00a0d2",
                              textDecoration: "underline",
                              fontWeight: "500",
                              cursor: "pointer",
                            }}
                            onClick={() => handleEdit(row)}
                          >
                            {row.name}
                          </span>
                        </td>
                        <td
                          style={{
                            ...tdStyle,
                            padding: "6px",
                            fontSize: "15px",
                            textAlign: "center",
                          }}
                        >
                          {row.broker_user_id}
                        </td>
                        <td
                          style={{
                            ...tdStyle,
                            padding: "6px",
                            fontSize: "15px",
                            textAlign: "center",
                          }}
                        >
                          {row.broker}
                        </td>
                        <td
                          style={{
                            ...tdStyle,
                            padding: "6px",
                            textAlign: "right",
                          }}
                        >
                          <div
                            style={{
                              display: "inline-block",
                              padding: "3px 6px",
                              borderRadius: "3px",
                              fontWeight: "600",
                              fontSize: "15px",
                              color: totalPnl < 0 ? "#cf1322" : "#52c41a",
                              borderColor: totalPnl < 0 ? "#ffa39e" : "#b7eb8f",
                            }}
                          >
                            {typeof totalPnl === "number"
                              ? totalPnl.toFixed(2)
                              : totalPnl}
                          </div>
                        </td>
                        <td
                          style={{
                            ...tdStyle,
                            padding: "6px",
                            textAlign: "center",
                          }}
                        >
                          <img
                            src={Log}
                            alt="icon"
                            className="logout_icon"
                            style={{
                              height: "23px",
                              width: "23px",
                            }}
                            onClick={() => {
                              masterSqOffSelected(row);
                            }}
                          />
                        </td>
                        <td
                          style={{
                            ...tdStyle,
                            padding: "6px",
                            textAlign: "center",
                          }}
                        >
                          {row.child_accounts.some((child) => child.live) ? (
                            <span
                              style={{
                                ...onStyle,
                                display: "inline-block",
                                padding: "2px 6px",
                                borderRadius: "10px",
                                color: "#52c41a",
                                // border: "1px solid #b7eb8f",
                                fontWeight: "600",
                                fontSize: "11px",
                              }}
                            >
                              YES
                            </span>
                          ) : (
                            <span
                              style={{
                                ...offStyle,
                                display: "inline-block",
                                padding: "2px 6px",
                                borderRadius: "10px",
                                color: "#cf1322",
                                // border: "1px solid #ffa39e",
                                fontWeight: "600",
                                fontSize: "11px",
                              }}
                            >
                              NO
                            </span>
                          )}
                        </td>
                        <td
                          style={{
                            ...tdStyle,
                            padding: "6px",
                            textAlign: "center",
                            // borderRight: "1px solid #f0f0f0",
                          }}
                        >
                          {row.copy_placement ? (
                            <span
                              style={{
                                ...onStyle,
                                display: "inline-block",
                                padding: "2px 6px",
                                borderRadius: "10px",
                                color: "#1890ff",
                                // border: "1px solid #91d5ff",
                                fontWeight: "600",
                                fontSize: "11px",
                              }}
                            >
                              ON
                            </span>
                          ) : (
                            <span
                              style={{
                                ...offStyle,
                                display: "inline-block",
                                padding: "2px 6px",
                                borderRadius: "10px",
                                color: "#8c8c8c",
                                // border: "1px solid #d9d9d9",
                                fontWeight: "600",
                                fontSize: "11px",
                              }}
                            >
                              OFF
                            </span>
                          )}
                        </td>
                        <td
                          style={{
                            ...tdStyle,
                            padding: "6px",
                            textAlign: "center",
                            // borderRight: "1px solid #f0f0f0",
                          }}
                        >
                          {row.copy_cancellation ? (
                            <span
                              style={{
                                ...onStyle,
                                display: "inline-block",
                                padding: "2px 6px",
                                borderRadius: "10px",
                                color: "#1890ff",
                                // border: "1px solid #91d5ff",
                                fontWeight: "600",
                                fontSize: "11px",
                              }}
                            >
                              ON
                            </span>
                          ) : (
                            <span
                              style={{
                                ...offStyle,
                                display: "inline-block",
                                padding: "2px 6px",
                                borderRadius: "10px",
                                color: "#8c8c8c",
                                // border: "1px solid #d9d9d9",
                                fontWeight: "600",
                                fontSize: "11px",
                              }}
                            >
                              OFF
                            </span>
                          )}
                        </td>
                        <td
                          style={{
                            ...tdStyle,
                            padding: "6px",
                            textAlign: "center",
                            // borderRight: "1px solid #f0f0f0",
                          }}
                        >
                          {row.copy_modification ? (
                            <span
                              style={{
                                ...onStyle,
                                display: "inline-block",
                                padding: "2px 6px",
                                borderRadius: "10px",
                                color: "#1890ff",
                                // border: "1px solid #91d5ff",
                                fontWeight: "600",
                                fontSize: "11px",
                              }}
                            >
                              ON
                            </span>
                          ) : (
                            <span
                              style={{
                                ...offStyle,
                                display: "inline-block",
                                padding: "2px 6px",
                                borderRadius: "10px",
                                color: "#8c8c8c",
                                // border: "1px solid #d9d9d9",
                                fontWeight: "600",
                                fontSize: "11px",
                              }}
                            >
                              OFF
                            </span>
                          )}
                        </td>
                        <td
                          style={{
                            ...tdStyle,
                            padding: "6px",
                            textAlign: "center",
                            fontSize: "15px",
                            fontWeight: "600",
                          }}
                        >
                          <span
                            style={{
                              display: "inline-block",
                              padding: "2px 6px",
                              borderRadius: "10px",
                              fontWeight: "600",
                              fontSize: "11px",
                            }}
                          >
                            {row.child_accounts.length}
                          </span>
                        </td>
                        <td
                          style={{
                            ...tdStyle,
                            padding: "6px",
                            textAlign: "center",
                            fontSize: "15px",
                            fontWeight: "600",
                          }}
                        >
                          <span
                            style={{
                              display: "inline-block",
                              padding: "2px 6px",
                              borderRadius: "10px",
                              fontWeight: "600",
                              fontSize: "11px",
                            }}
                          >
                            {row.child_accounts.reduce(
                              (total, child) => total + (child.multiplier || 0),
                              0
                            )}
                          </span>
                        </td>
                      </tr>
                      {expandedRowIndex === index && (
                        <tr>
                          <td
                            colSpan={11}
                            style={{ padding: 0, background: "white" }}
                          >
                            <div
                              style={{
                                maxHeight: "300px",
                                overflowY: "auto",
                                position: "relative",
                                width: "100%",
                                marginLeft: "30px",
                                marginTop: "8px",
                                marginBottom: "8px",
                              }}
                            >
                              <table
                                className="mini-table"
                                style={{
                                  width: "100%",
                                  borderCollapse: "collapse",
                                  boxShadow: "0 1px 4px rgba(0,0,0,0.1)",
                                  borderRadius: "6px",
                                  overflow: "visible",
                                }}
                              >
                                <thead
                                  style={{
                                    position: "sticky",
                                    top: "0",
                                    zIndex: "40",
                                    backgroundColor: "#e6f0ff",
                                  }}
                                >
                                  <tr>
                                    <th
                                      style={{
                                        ...thStyleM,
                                        padding: "6px 8px",
                                        fontSize: "15px",
                                        fontWeight: "600",
                                        color: "#1f2937",
                                        textAlign: "center",
                                      }}
                                    >
                                      {" "}
                                    </th>
                                    <th
                                      style={{
                                        ...thStyleM,
                                        padding: "6px 8px",
                                        fontSize: "15px",
                                        fontWeight: "600",
                                        color: "#1f2937",
                                        textAlign: "center",
                                      }}
                                    >
                                      User ID
                                    </th>
                                    <th
                                      style={{
                                        ...thStyleM,
                                        padding: "6px 8px",
                                        fontSize: "15px",
                                        fontWeight: "600",
                                        color: "#1f2937",
                                        textAlign: "center",
                                      }}
                                    >
                                      Broker
                                    </th>
                                    <th
                                      style={{
                                        ...thStyleM,
                                        padding: "6px 8px",
                                        fontSize: "15px",
                                        fontWeight: "600",
                                        color: "#1f2937",
                                        textAlign: "center",
                                      }}
                                    >
                                      Multiplier
                                    </th>
                                    <th
                                      style={{
                                        ...thStyleM,
                                        padding: "6px 8px",
                                        fontSize: "15px",
                                        fontWeight: "600",
                                        color: "#1f2937",
                                        textAlign: "center",
                                      }}
                                    >
                                      Sq Off
                                    </th>
                                    <th
                                      style={{
                                        ...thStyleM,
                                        padding: "6px 8px",
                                        fontSize: "15px",
                                        fontWeight: "600",
                                        color: "#1f2937",
                                        textAlign: "right",
                                      }}
                                    >
                                      P&L
                                    </th>
                                  </tr>
                                </thead>
                                <tbody>
                                  {[ row, ...row.child_accounts ].map(
                                    (account, idx) => {
                                      const accountData =
                                        masterChildPnL[ row.id ]
                                          ?.broker_user_pnls?.[
                                        account.broker_user_id
                                        ];
                                      const accountPnl =
                                        accountData?.total_pnl || 0;
                                      const accountSymbols =
                                        accountData?.symbols || [];
                                      const hasPositions =
                                        accountSymbols &&
                                        accountSymbols.length > 0;

                                      return (
                                        <React.Fragment key={idx}>
                                          <tr
                                            style={{
                                              backgroundColor:
                                                account === row
                                                  ? "#f0f9ff"
                                                  : idx % 2 === 0
                                                    ? "#ffffff"
                                                    : "#f9fafb",
                                            }}
                                          >
                                            <td>
                                              <span
                                                onClick={() =>
                                                  handleExpandPostionsRow(
                                                    account.broker_user_id
                                                  )
                                                }
                                                style={{
                                                  display: "inline-flex",
                                                  alignItems: "center",
                                                  justifyContent: "center",
                                                  width: "20px",
                                                  height: "20px",
                                                  borderRadius: "3px",
                                                  backgroundColor: hasPositions
                                                    ? "#e6f7ff"
                                                    : "#f0f0f0",
                                                  border: "1px solid",
                                                  borderColor: hasPositions
                                                    ? "#91d5ff"
                                                    : "#d9d9d9",
                                                  color: hasPositions
                                                    ? "#1890ff"
                                                    : "#8c8c8c",
                                                  cursor: "pointer",
                                                  transition: "all 0.3s",
                                                  fontSize: "14px",
                                                }}
                                              >
                                                {selectedRow ===
                                                  account.broker_user_id
                                                  ? "−"
                                                  : "+"}
                                              </span>
                                            </td>
                                            <td
                                              style={{
                                                ...tdStyle,
                                                padding: "4px 6px",
                                                fontSize: "15px",
                                                display: "flex",
                                                alignItems: "center",
                                              }}
                                            >
                                              <span
                                                style={{
                                                  color:
                                                    account === row
                                                      ? "#00a0d2"
                                                      : "#333",
                                                  textDecoration:
                                                    account === row
                                                      ? "underline"
                                                      : "none",
                                                  fontWeight:
                                                    account === row
                                                      ? "600"
                                                      : "normal",
                                                  cursor:
                                                    account === row
                                                      ? "pointer"
                                                      : "default",
                                                }}
                                              >
                                                {account.broker_user_id}
                                              </span>
                                              {account === row ? (
                                                <span
                                                  style={{
                                                    display: "inline-block",
                                                    backgroundColor: "#e6f7ff",
                                                    color: "#1890ff",
                                                    fontSize: "10px",
                                                    marginLeft: "4px",
                                                    fontWeight: "600",
                                                    padding: "2px 4px",
                                                    borderRadius: "3px",
                                                  }}
                                                >
                                                  MASTER
                                                </span>
                                              ) : (
                                                <span
                                                  style={{
                                                    display: "inline-block",
                                                    backgroundColor: "#f9f0ff",
                                                    color: "#722ed1",
                                                    borderRadius: "3px",
                                                    fontSize: "10px",
                                                    marginLeft: "4px",
                                                    fontWeight: "600",
                                                    padding: "2px 4px",
                                                  }}
                                                >
                                                  CHILD
                                                </span>
                                              )}
                                            </td>
                                            <td
                                              style={{
                                                ...tdStyle,
                                                padding: "4px 6px",
                                                fontSize: "15px",
                                              }}
                                            >
                                              {account.broker}
                                            </td>
                                            <td
                                              style={{
                                                ...tdStyle,
                                                padding: "4px 6px",
                                                fontSize: "15px",
                                                textAlign: "center",
                                              }}
                                            >
                                              <span
                                                style={{
                                                  display: "inline-block",
                                                  padding: "1px 5px",
                                                  borderRadius: "10px",
                                                  fontWeight: "600",
                                                  fontSize: "11px",
                                                }}
                                              >
                                                {account.multiplier
                                                  ? account.multiplier
                                                  : "1"}
                                              </span>
                                            </td>
                                            <td
                                              style={{
                                                ...tdStyle,
                                                padding: "6px",
                                                textAlign: "center",
                                                display: "flex",
                                                alignItems: "center",
                                                justifyContent: "center",
                                              }}
                                            >
                                              <img
                                                src={Log}
                                                alt="icon"
                                                className="logout_icon"
                                                style={{
                                                  height: "20px",
                                                  width: "20px",
                                                  cursor: "pointer",
                                                }}
                                                onClick={() =>
                                                  childSqOffSelected(
                                                    account,
                                                    row
                                                  )
                                                }
                                              />
                                            </td>
                                            <td
                                              style={{
                                                ...tdStyle,
                                                padding: "4px 6px",
                                                textAlign: "right",
                                              }}
                                            >
                                              <div
                                                style={{
                                                  display: "inline-block",
                                                  padding: "2px 6px",
                                                  borderRadius: "3px",
                                                  fontWeight: "600",
                                                  fontSize: "12px",
                                                  width: "70px",

                                                  color:
                                                    accountPnl < 0
                                                      ? "#cf1322"
                                                      : "#52c41a",
                                                  borderColor:
                                                    accountPnl < 0
                                                      ? "#ffa39e"
                                                      : "#b7eb8f",
                                                }}
                                              >
                                                {accountPnl.toFixed(2)}
                                              </div>
                                            </td>
                                          </tr>

                                          {selectedRow ===
                                            account.broker_user_id &&
                                            hasPositions && (
                                              <>
                                                <tr>
                                                  <td
                                                    colSpan={17}
                                                    style={{
                                                      background: "white",
                                                    }}
                                                  >
                                                    <div
                                                      style={{
                                                        margin:
                                                          "10px 10px 10px 50px",
                                                        backgroundColor:
                                                          "#f0f7ff",
                                                        borderRadius: "6px",
                                                        boxShadow:
                                                          "0 1px 3px rgba(0,0,0,0.1)",
                                                        overflow: "hidden",
                                                      }}
                                                    >
                                                      <table
                                                        style={{
                                                          width: "100%",
                                                          // borderCollapse: "collapse",
                                                        }}
                                                      >
                                                        <thead>
                                                          <tr
                                                            style={{
                                                              backgroundColor:
                                                                "#e6f0ff",
                                                            }}
                                                          >
                                                            <th
                                                              style={{
                                                                padding:
                                                                  "5px 8px",
                                                                textAlign:
                                                                  "center",
                                                                fontSize:
                                                                  "12px",
                                                                fontWeight:
                                                                  "600",
                                                                color:
                                                                  "#2c5282",
                                                                borderBottom:
                                                                  "1px solid #d0e3ff",
                                                              }}
                                                            >
                                                              Sq Off
                                                            </th>
                                                            <th
                                                              style={{
                                                                padding:
                                                                  "5px 8px",
                                                                textAlign:
                                                                  "right",
                                                                fontSize:
                                                                  "12px",
                                                                fontWeight:
                                                                  "600",
                                                                color:
                                                                  "#2c5282",
                                                                borderBottom:
                                                                  "1px solid #d0e3ff",
                                                                width: "60px",
                                                              }}
                                                            >
                                                              P&L
                                                            </th>
                                                            <th
                                                              style={{
                                                                padding:
                                                                  "5px 8px",
                                                                textAlign:
                                                                  "center",
                                                                fontSize:
                                                                  "12px",
                                                                fontWeight:
                                                                  "600",
                                                                color:
                                                                  "#2c5282",
                                                                borderBottom:
                                                                  "1px solid #d0e3ff",
                                                                width: "50px",
                                                              }}
                                                            >
                                                              Exchange
                                                            </th>
                                                            <th
                                                              style={{
                                                                padding:
                                                                  "5px 8px",
                                                                textAlign:
                                                                  "left",
                                                                fontSize:
                                                                  "12px",
                                                                fontWeight:
                                                                  "600",
                                                                color:
                                                                  "#2c5282",
                                                                borderBottom:
                                                                  "1px solid #d0e3ff",
                                                              }}
                                                            >
                                                              Symbol
                                                            </th>
                                                            <th
                                                              style={{
                                                                padding:
                                                                  "5px 8px",
                                                                textAlign:
                                                                  "center",
                                                                fontSize:
                                                                  "12px",
                                                                fontWeight:
                                                                  "600",
                                                                color:
                                                                  "#2c5282",
                                                                borderBottom:
                                                                  "1px solid #d0e3ff",
                                                              }}
                                                            >
                                                              Net Qty
                                                            </th>
                                                            <th
                                                              style={{
                                                                padding:
                                                                  "5px 8px",
                                                                textAlign:
                                                                  "center",
                                                                fontSize:
                                                                  "12px",
                                                                fontWeight:
                                                                  "600",
                                                                color:
                                                                  "#2c5282",
                                                                borderBottom:
                                                                  "1px solid #d0e3ff",
                                                                width: "60px",
                                                              }}
                                                            >
                                                              LTP
                                                            </th>
                                                            <th
                                                              style={{
                                                                padding:
                                                                  "5px 8px",
                                                                textAlign:
                                                                  "center",
                                                                fontSize:
                                                                  "12px",
                                                                fontWeight:
                                                                  "600",
                                                                color:
                                                                  "#2c5282",
                                                                borderBottom:
                                                                  "1px solid #d0e3ff",
                                                              }}
                                                            >
                                                              Buy Qty
                                                            </th>
                                                            <th
                                                              style={{
                                                                padding:
                                                                  "5px 8px",
                                                                textAlign:
                                                                  "right",
                                                                fontSize:
                                                                  "12px",
                                                                fontWeight:
                                                                  "600",
                                                                color:
                                                                  "#2c5282",
                                                                borderBottom:
                                                                  "1px solid #d0e3ff",
                                                              }}
                                                            >
                                                              Buy Value
                                                            </th>
                                                            <th
                                                              style={{
                                                                padding:
                                                                  "5px 8px",
                                                                textAlign:
                                                                  "center",
                                                                fontSize:
                                                                  "12px",
                                                                fontWeight:
                                                                  "600",
                                                                color:
                                                                  "#2c5282",
                                                                borderBottom:
                                                                  "1px solid #d0e3ff",
                                                              }}
                                                            >
                                                              Sell Qty
                                                            </th>
                                                            <th
                                                              style={{
                                                                padding:
                                                                  "5px 8px",
                                                                textAlign:
                                                                  "right",
                                                                fontSize:
                                                                  "12px",
                                                                fontWeight:
                                                                  "600",
                                                                color:
                                                                  "#2c5282",
                                                                borderBottom:
                                                                  "1px solid #d0e3ff",
                                                              }}
                                                            >
                                                              Sell Value
                                                            </th>
                                                            <th
                                                              style={{
                                                                padding:
                                                                  "5px 8px",
                                                                textAlign:
                                                                  "center",
                                                                fontSize:
                                                                  "12px",
                                                                fontWeight:
                                                                  "600",
                                                                color:
                                                                  "#2c5282",
                                                                borderBottom:
                                                                  "1px solid #d0e3ff",
                                                              }}
                                                            >
                                                              Type
                                                            </th>
                                                          </tr>
                                                        </thead>
                                                        <tbody>
                                                          {accountSymbols.map(
                                                            (
                                                              position,
                                                              posIdx
                                                            ) => (
                                                              <tr
                                                                key={`${idx}-${posIdx}`}
                                                                style={{
                                                                  backgroundColor:
                                                                    posIdx %
                                                                      2 ===
                                                                      0
                                                                      ? "#f8faff"
                                                                      : "#ffffff",
                                                                  borderBottom:
                                                                    "1px solid #e6effd",
                                                                }}
                                                              >
                                                                <td
                                                                  style={{
                                                                    padding:
                                                                      "4px 6px",
                                                                    textAlign:
                                                                      "center",
                                                                  }}
                                                                >
                                                                  <img
                                                                    src={Log}
                                                                    alt="icon"
                                                                    style={{
                                                                      height:
                                                                        "16px",
                                                                      width:
                                                                        "16px",
                                                                      cursor:
                                                                        "pointer",
                                                                    }}
                                                                    onClick={() =>
                                                                      particularPositionsSq(
                                                                        account,
                                                                        row,
                                                                        position
                                                                      )
                                                                    }
                                                                  />
                                                                </td>
                                                                <td
                                                                  style={{
                                                                    padding:
                                                                      "4px 6px",
                                                                    fontSize:
                                                                      "12px",
                                                                    fontWeight:
                                                                      "600",
                                                                    textAlign:
                                                                      "right",
                                                                    color:
                                                                      position.pnl <
                                                                        0
                                                                        ? "#e53e3e"
                                                                        : "#38a169",
                                                                  }}
                                                                >
                                                                  {position.pnl.toFixed(
                                                                    2
                                                                  )}
                                                                </td>
                                                                <td
                                                                  style={{
                                                                    padding:
                                                                      "4px 6px",
                                                                    fontSize:
                                                                      "14px",
                                                                    textAlign:
                                                                      "center",
                                                                    color:
                                                                      "#4a5568",
                                                                  }}
                                                                >
                                                                  {
                                                                    position.exchange
                                                                  }
                                                                </td>
                                                                <td
                                                                  style={{
                                                                    padding:
                                                                      "4px 6px",
                                                                    fontSize:
                                                                      "14px",
                                                                    fontWeight:
                                                                      "500",
                                                                    color:
                                                                      "#2d3748",
                                                                  }}
                                                                >
                                                                  {
                                                                    position.symbol
                                                                  }
                                                                </td>
                                                                <td
                                                                  style={{
                                                                    padding:
                                                                      "4px 6px",
                                                                    fontSize:
                                                                      "14px",
                                                                    textAlign:
                                                                      "center",
                                                                    fontWeight:
                                                                      position.net_qty !==
                                                                        0
                                                                        ? "600"
                                                                        : "normal",
                                                                    color:
                                                                      "#4a5568",
                                                                  }}
                                                                >
                                                                  {
                                                                    position.net_qty
                                                                  }
                                                                </td>
                                                                <td
                                                                  style={{
                                                                    padding:
                                                                      "4px 6px",
                                                                    fontSize:
                                                                      "14px",
                                                                    textAlign:
                                                                      "center",
                                                                    color:
                                                                      "#4a5568",
                                                                  }}
                                                                >
                                                                  {position.ltp}
                                                                </td>
                                                                <td
                                                                  style={{
                                                                    padding:
                                                                      "4px 6px",
                                                                    fontSize:
                                                                      "14px",
                                                                    textAlign:
                                                                      "center",
                                                                    color:
                                                                      "#4a5568",
                                                                  }}
                                                                >
                                                                  {
                                                                    position.buy_qty
                                                                  }
                                                                </td>
                                                                <td
                                                                  style={{
                                                                    padding:
                                                                      "4px 6px",
                                                                    fontSize:
                                                                      "14px",
                                                                    textAlign:
                                                                      "right",
                                                                    color:
                                                                      "#4a5568",
                                                                  }}
                                                                >
                                                                  {position.buy_value.toFixed(
                                                                    2
                                                                  )}
                                                                </td>
                                                                <td
                                                                  style={{
                                                                    padding:
                                                                      "8px 12px",
                                                                    fontSize:
                                                                      "14px",
                                                                    textAlign:
                                                                      "center",
                                                                    color:
                                                                      "#4a5568",
                                                                  }}
                                                                >
                                                                  {
                                                                    position.sell_qty
                                                                  }
                                                                </td>
                                                                <td
                                                                  style={{
                                                                    padding:
                                                                      "4px 6px",
                                                                    fontSize:
                                                                      "14px",
                                                                    textAlign:
                                                                      "right",
                                                                    color:
                                                                      "#4a5568",
                                                                  }}
                                                                >
                                                                  {position.sell_value.toFixed(
                                                                    2
                                                                  )}
                                                                </td>
                                                                <td
                                                                  style={{
                                                                    padding:
                                                                      "4px 6px",
                                                                    fontSize:
                                                                      "14px",
                                                                    textAlign:
                                                                      "center",
                                                                    color:
                                                                      position.transaction_type ===
                                                                        "BUY"
                                                                        ? "#3182ce"
                                                                        : "#e53e3e",
                                                                    fontWeight:
                                                                      "500",
                                                                  }}
                                                                >
                                                                  {
                                                                    position.transaction_type
                                                                  }
                                                                </td>
                                                              </tr>
                                                            )
                                                          )}
                                                        </tbody>
                                                      </table>
                                                    </div>
                                                  </td>
                                                </tr>
                                              </>
                                            )}
                                        </React.Fragment>
                                      );
                                    }
                                  )}
                                </tbody>
                              </table>
                            </div>
                          </td>
                        </tr>
                      )}
                    </React.Fragment>
                  );
                })}
              </tbody>
            </table>
          </div>
          <button
            style={{ zIndex: "0", marginLeft: "88%" }}
            onClick={() => errorContainerRef.current.toggleCollapse()}
            className="button"
            id="collapse"
          >
            {collapsed ? "Expand" : "Collapse"}
          </button>
          <OptimizedErrorContainer
            msgs={msgs}
            ref={errorContainerRef}
            handleClearLogs={handleClearLogs}
          />
        </div>
        <OptimizedRightNav />
      </div>
      {filterPopup === "name" && (
        <div
          ref={filterPopupRef}
          style={{
            position: "absolute",
            top: `${popupPosition.top + 5}px`,
            left: `${popupPosition.left}px`,
            background: "#ffffff",
            border: "1px solid #e0e0e0",
            borderRadius: "8px",
            padding: "3px 0 3px 3px",
            boxShadow: "0 4px 12px rgba(0, 0, 0, 0.15)",
            zIndex: 1000,
            minWidth: "150px",
            maxWidth: "200px",
          }}
        >
          <div style={{ padding: "8px" }}>
            <div
              style={{
                borderBottom: "1px solid #e0e0e0",
                paddingBottom: "8px",
                marginBottom: "8px",
                display: "flex",
                justifyContent: "space-between",
                alignItems: "center",
              }}
            >
              <span style={{ fontWeight: "bold", color: "#1976d2" }}>
                Filter: Name
              </span>
              <span
                style={{
                  fontSize: "12px",
                  color: "#666",
                  backgroundColor: "#f5f5f5",
                  padding: "2px 6px",
                  borderRadius: "4px",
                }}
              >
                {getDynamicUniqueValues("name").length} items
              </span>
            </div>
            <label
              style={{
                display: "flex",
                alignItems: "center",
                padding: "4px",
                cursor: "pointer",
                fontWeight: "500",
                color: "#333",
                marginBottom: "4px",
                backgroundColor: "#f8f9fa",
                borderRadius: "4px",
              }}
            >
              <input
                type="checkbox"
                checked={
                  tempFilters[ "name" ] &&
                  getDynamicUniqueValues("name").every((opt) =>
                    tempFilters[ "name" ].includes(opt)
                  )
                }
                onChange={() => handleSelectAllFilters("name")}
                style={{ marginRight: "8px" }}
              />
              <span>Select All</span>
            </label>
            <div className="scrollable-options">
              {getDynamicUniqueValues("name").map((item) => {
                const isSelected = tempFilters[ "name" ]?.includes(item) || false;
                return (
                  <div
                    key={item}
                    style={{
                      display: "flex",
                      alignItems: "center",
                      padding: "4px",
                      cursor: "pointer",
                      borderBottom: "1px solid #f0f0f0",
                      backgroundColor: isSelected ? "#f0f7ff" : "transparent",
                    }}
                    onClick={() => handleFilterChange("name", item)}
                  >
                    <input
                      type="checkbox"
                      checked={isSelected}
                      onChange={() => { }}
                      style={{ marginRight: "8px" }}
                    />
                    <span
                      style={{
                        color: isSelected ? "#1976d2" : "#444",
                        fontWeight: isSelected ? "500" : "normal",
                      }}
                    >
                      {item || "(Empty)"}
                    </span>
                  </div>
                );
              })}
            </div>
            <div
              style={{
                fontSize: "12px",
                color: "#666",
                margin: "8px 0",
                display: "flex",
                justifyContent: "space-between",
              }}
            >
              <span>
                {tempFilters[ "name" ]?.length || 0} of{" "}
                {getDynamicUniqueValues("name").length} selected
              </span>
              {tempFilters[ "name" ]?.length > 0 && (
                <span
                  style={{ color: "#1976d2", cursor: "pointer" }}
                  onClick={() =>
                    setTempFilters({ ...tempFilters, [ "name" ]: [] })
                  }
                >
                  Clear selection
                </span>
              )}
            </div>
            <div
              style={{ display: "flex", gap: "10px", justifyContent: "center" }}
            >
              <button onClick={handleCancelFilter} className="cancel-btn">
                Cancel
              </button>
              <button onClick={handleApplyFilter} className="apply-btn">
                Apply Filter
              </button>
            </div>
          </div>
        </div>
      )}

      {isModalOpen && (
        <>
          <div style={overlayStyle} onClick={closeModal}></div>
          <div style={modalStyle1}>
            <p>Master Account</p>
            <select
              style={{ width: "100%", padding: "10px", marginBottom: "10px" }}
              onChange={(e) =>
                setSelectedMasterAccount(
                  masterChildAccounts.find(
                    (row) => row.userId === e.target.value
                  )
                )
              }
            >
              <option value="">---Select Master Account---</option>
              {masterChildAccounts.map((row) => (
                <option key={row.userId} value={row.userId}>
                  {`${row.name}: ${row.userId} - ${row.broker}`}
                </option>
              ))}
            </select>
            <button
              style={{
                ...closeButtonStyle,
                cursor:
                  masterChildAccounts.length === 0 ? "not-allowed" : "pointer",
                opacity: masterChildAccounts.length === 0 ? 0.6 : 1,
                backgroundColor:
                  masterChildAccounts.length === 0 ? "#6c8cbf" : "#007bff",
              }}
              onClick={handleSelectChildAccounts}
              disabled={masterChildAccounts.length === 0}
            >
              Select Child Accounts
            </button>
          </div>
        </>
      )}
      {deleteModal && (
        <>
          <div style={overlayStyle} onClick={closeModalDelete}></div>
          <div style={modalStyle1}>
            {errorMessage ? (
              <div style={{ color: "red" }}>{errorMessage}</div>
            ) : (
              <div>Do you need to delete the master_child accounts</div>
            )}
            <button
              style={{
                ...DeleteButtonStyle,
                cursor: "pointer",
              }}
              onClick={handleDeleteSelected}
            >
              Delete
            </button>
            <button
              style={{
                ...cancelButtonStyle,
                cursor: "pointer",
              }}
              onClick={closeModalDelete}
            >
              Cancel
            </button>
          </div>
        </>
      )}
      {sqOffModal && (
        <>
          <div style={overlayStyle} onClick={closeModalSqOff}></div>
          <div style={modalStyle1}>
            {errorMessage ? (
              <div style={{ color: "red", fontSize: "18px" }}>
                {errorMessage}
              </div>
            ) : (
              <div>Do you need to Square off the master_child accounts</div>
            )}
            <button
              style={{
                ...yesButtonStyle,
                cursor: "pointer",
              }}
              onClick={SqOffSelected}
            >
              YES
            </button>
            <button
              style={{
                ...noButtonStyle,
                cursor: "pointer",
              }}
              onClick={closeModalSqOff}
            >
              NO
            </button>
          </div>
        </>
      )}
      {isTradeModalOpen && (
        <div>
          <Draggable handle=".trade-popup-header">
            <div className="trade-popup-container">
              <div className="trade-popup-header">
                <h2 className="trade-popup-title">Trade</h2>
                <button onClick={closeTradeModal} className="trade-popup-close">
                  ×
                </button>
              </div>
              <form onSubmit={handleTradeSubmit} className="trade-form">
                {/* Form validation messages - moved to top */}
                <div className="validation-messages">
                  {quantityError && (
                    <div className="error-message">{quantityError}</div>
                  )}

                  {priceWarning && (
                    <div className="warning-message">{priceWarning}</div>
                  )}

                  {!isFormValid() &&
                    !priceWarning &&
                    !quantityError &&
                    formData.exchange && (
                      <div className="warning-message">
                        {!formData.symbol
                          ? "Symbol is required"
                          : !formData.transactiontype
                            ? "Transaction type is required"
                            : !formData.producttype
                              ? "Product type is required"
                              : !formData.ordertype
                                ? "Order type is required"
                                : !formData.ordercategory
                                  ? "Order category is required (Regular/BO/CO)"
                                  : !formData.quantity
                                    ? "Quantity is required"
                                    : formData.ordertype === "LIMIT" && !formData.price
                                      ? "Price is required for LIMIT orders"
                                      : (formData.ordertype === "STOP_LOSS" ||
                                        formData.ordertype === "SL_MARKET") &&
                                        !formData.triggerPrice
                                        ? "Trigger price is required"
                                        : !selectedAccountId
                                          ? "Please select an account"
                                          : ""}
                      </div>
                    )}
                </div>

                <div className="transaction-type">
                  <label className="radio-label">
                    <input
                      type="radio"
                      name="transactiontype"
                      value="BUY"
                      checked={formData.transactiontype === "BUY"}
                      onChange={handleInputChange}
                    />{" "}
                    Buy
                  </label>
                  <label className="radio-label">
                    <input
                      type="radio"
                      name="transactiontype"
                      value="SELL"
                      checked={formData.transactiontype === "SELL"}
                      onChange={handleInputChange}
                    />{" "}
                    Sell
                  </label>
                  <div className="symbol-exchange-container">
                    <div className="exchange-container">
                      <select
                        name="exchange"
                        value={formData.exchange}
                        onChange={handleExchangeChange}
                        className={!formData.exchange ? "error" : ""}
                        required
                      >
                        <option value="">Select</option>
                        <option value="NSE">NSE</option>
                        <option value="BSE">BSE</option>
                        <option value="NFO">NFO</option>
                      </select>
                    </div>
                    <div className="symbol-container">
                      <input
                        type="text"
                        name="symbol"
                        value={formData.symbol}
                        onChange={(e) => {
                          const value = e.target.value;
                          setFormData((prevState) => ({
                            ...prevState,
                            symbol: value,
                          }));
                          if (formData.exchange) {
                            debounceFilterSymbols(value);
                          }
                        }}
                        onKeyDown={handleSymbolKeyDown}
                        placeholder={
                          !formData.exchange
                            ? "Select Exchange First"
                            : "Enter Symbol"
                        }
                        className={`symbol-input ${!formData.symbol && formData.exchange ? "error" : ""
                          }`}
                        autoComplete="off"
                        required
                        ref={symbolInputRef}
                        disabled={!formData.exchange}
                      />
                      {isLoadingSymbols && formData.exchange && (
                        <div className="loading-indicator">
                          Loading symbols...
                        </div>
                      )}
                      {showDropdown &&
                        filteredSymbols.length > 0 &&
                        formData.exchange && (
                          <ul className="symbol-dropdown">
                            {filteredSymbols.map((symbol, index) => {
                              const symbolText = symbol.Symbol;
                              let displayText = symbolText;

                              const indexMatch =
                                /^(NIFTY|BANKNIFTY|FINNIFTY)/i.exec(symbolText);
                              const monthMatch =
                                /(JAN|FEB|MAR|APR|MAY|JUN|JUL|AUG|SEP|OCT|NOV|DEC)/i.exec(
                                  symbolText
                                );
                              const typeMatch = /(FUT|CE|PE)/i.exec(symbolText);
                              const yearMatch =
                                /(JAN|FEB|MAR|APR|MAY|JUN|JUL|AUG|SEP|OCT|NOV|DEC)\d{2}/i.exec(
                                  symbolText
                                );
                              const dayMatch = monthMatch
                                ? symbolText
                                  .substring(0, monthMatch.index)
                                  .match(/\d{2}$/)
                                : null;
                              const strikeMatch =
                                monthMatch && typeMatch
                                  ? symbolText
                                    .substring(
                                      monthMatch.index + monthMatch[ 0 ].length,
                                      typeMatch.index
                                    )
                                    .match(/\d+/)
                                  : null;

                              if (indexMatch && monthMatch) {
                                const index = indexMatch[ 0 ];
                                const day = dayMatch ? dayMatch[ 0 ] : "";
                                const month = monthMatch[ 0 ];
                                const year = yearMatch
                                  ? yearMatch[ 0 ].substring(month.length)
                                  : "";
                                const type = typeMatch ? typeMatch[ 0 ] : "";
                                const strike = strikeMatch
                                  ? strikeMatch[ 0 ].length > 2
                                    ? strikeMatch[ 0 ].substring(2)
                                    : strikeMatch[ 0 ]
                                  : "";

                                displayText = (
                                  <span>
                                    <span
                                      style={{
                                        fontWeight: "bold",
                                        color: "#1890ff",
                                      }}
                                    >
                                      {index}{" "}
                                    </span>
                                    <span style={{ color: "#52c41a" }}>
                                      {day} {month}
                                      {year}{" "}
                                    </span>
                                    {strike && (
                                      <span style={{ color: "#fa8c16" }}>
                                        {strike}{" "}
                                      </span>
                                    )}
                                    {type && (
                                      <span
                                        style={{
                                          color:
                                            type === "CE"
                                              ? "green"
                                              : type === "PE"
                                                ? "red"
                                                : "#f5222d",
                                        }}
                                      >
                                        {type}{" "}
                                      </span>
                                    )}
                                  </span>
                                );
                              }

                              return (
                                <li
                                  key={index}
                                  onClick={() => handleSymbolSelect(symbol)}
                                  className={`symbol-dropdown-item ${selectedSymbolIndex === index
                                    ? "selected"
                                    : ""
                                    }`}
                                  onMouseEnter={() =>
                                    setSelectedSymbolIndex(index)
                                  }
                                >
                                  {displayText}
                                </li>
                              );
                            })}
                          </ul>
                        )}
                    </div>
                  </div>
                </div>
                <div className="radio-group">
                  <label className="radio-label">
                    <input
                      type="radio"
                      name="ordercategory"
                      value="REGULAR"
                      checked={formData.ordercategory === "REGULAR"}
                      onChange={handleInputChange}
                    />{" "}
                    Regular
                  </label>
                  <label className="radio-label">
                    <input
                      type="radio"
                      name="ordercategory"
                      value="BO"
                      checked={formData.ordercategory === "BO"}
                      onChange={handleInputChange}
                    />{" "}
                    BO
                  </label>
                  <label className="radio-label">
                    <input
                      type="radio"
                      name="ordercategory"
                      value="CO"
                      checked={formData.ordercategory === "CO"}
                      onChange={handleInputChange}
                    />{" "}
                    CO
                  </label>
                </div>
                <div className="radio-group">
                  <label className="radio-label">
                    <input
                      type="radio"
                      name="producttype"
                      value="INTRADAY"
                      checked={formData.producttype === "INTRADAY"}
                      onChange={handleInputChange}
                    />{" "}
                    Intraday
                  </label>
                  <label className="radio-label">
                    <input
                      type="radio"
                      name="producttype"
                      value="DELIVERY"
                      checked={formData.producttype === "DELIVERY"}
                      onChange={handleInputChange}
                    />{" "}
                    Delivery
                  </label>
                  <label className="radio-label">
                    <input
                      type="radio"
                      name="producttype"
                      value="NORMAL"
                      checked={formData.producttype === "NORMAL"}
                      onChange={handleInputChange}
                    />{" "}
                    Normal
                  </label>
                </div>
                <div className="radio-group">
                  <label className="radio-label">
                    <input
                      type="radio"
                      name="ordertype"
                      value="LIMIT"
                      checked={formData.ordertype === "LIMIT"}
                      onChange={handleInputChange}
                    />{" "}
                    Limit
                  </label>
                  <label className="radio-label">
                    <input
                      type="radio"
                      name="ordertype"
                      value="MARKET"
                      checked={formData.ordertype === "MARKET"}
                      onChange={handleInputChange}
                    />{" "}
                    Market
                  </label>
                  <label className="radio-label">
                    <input
                      type="radio"
                      name="ordertype"
                      value="STOP_LOSS"
                      checked={formData.ordertype === "STOP_LOSS"}
                      onChange={handleInputChange}
                    />{" "}
                    Stop_Loss
                  </label>
                  <label className="radio-label">
                    <input
                      type="radio"
                      name="ordertype"
                      value="SL_MARKET"
                      checked={formData.ordertype === "SL_MARKET"}
                      onChange={handleInputChange}
                    />{" "}
                    Sl_Market
                  </label>
                </div>

                <div className="order-details">
                  <div className="input-group">
                    <label className="input-label">
                      Qty <span style={{ color: "red" }}>*</span>
                    </label>
                    <input
                      type="text"
                      inputMode="numeric"
                      name="quantity"
                      value={formData?.quantity}
                      placeholder="1"
                      onChange={(e) => {
                        const rawValue = e.target.value;
                        const cleanValue = rawValue.replace(/\D/g, "");

                        setFormData((prevState) => ({
                          ...prevState,
                          quantity: cleanValue,
                        }));

                        // Validate quantity
                        validateQuantity(cleanValue);
                      }}
                      onMouseEnter={handleMouseEnter}
                      onMouseLeave={handleMouseLeave}
                      className="input-field"
                    />
                    {showLotSize && (
                      <span className="lot-size">[Lot: {lotSize}]</span>
                    )}
                    <span style={tooltipTextStyle}>
                      <div style={arrowStyle}></div>
                      Quantity should be multiple of lot size for derivatives
                    </span>
                  </div>

                  <div className="input-group">
                    <label className="input-label">
                      Price{" "}
                      {formData.ordertype === "LIMIT" && (
                        <span style={{ color: "red" }}>*</span>
                      )}
                    </label>
                    <input
                      type="text"
                      inputMode="numeric"
                      name="price"
                      value={formData.price}
                      placeholder="0"
                      onBlur={() => {
                        if (formData.ordertype === "LIMIT" && symbolLTP) {
                          validatePriceAgainstLTP();
                        }
                      }}
                      onChange={(e) => {
                        const rawValue = e.target.value;
                        const cleanValue = rawValue.replace(/\D/g, "");

                        setFormData((prevState) => {
                          const updatedState = {
                            ...prevState,
                            price: cleanValue,
                          };

                          if (
                            (formData.ordertype === "LIMIT" ||
                              formData.ordertype === "MARKET") &&
                            symbolLTP &&
                            cleanValue
                          ) {
                            const price = parseInt(cleanValue, 10);
                            const ltp = parseFloat(symbolLTP);

                            if (
                              price > ltp &&
                              formData.transactiontype === "BUY"
                            ) {
                              setPriceWarning(
                                `Warning: Your buy price (${cleanValue}) is more than the current LTP (${ltp})`
                              );
                            } else if (
                              price < ltp &&
                              formData.transactiontype === "SELL"
                            ) {
                              setPriceWarning(
                                `Warning: Your sell price (${cleanValue}) is less than the current LTP (${ltp})`
                              );
                            } else {
                              setPriceWarning("");
                            }
                          }

                          return updatedState;
                        });
                      }}
                      disabled={
                        formData.ordertype === "MARKET" ||
                        formData.ordertype === "SL_MARKET"
                      }
                      className="input-field"
                    />
                  </div>

                  <div className="input-group">
                    <label className="input-label">
                      Trig.Price{" "}
                      {(formData.ordertype === "STOP_LOSS" ||
                        formData.ordertype === "SL_MARKET") && (
                          <span style={{ color: "red" }}>*</span>
                        )}
                    </label>
                    <input
                      type="text"
                      inputMode="numeric"
                      name="triggerPrice"
                      value={formData?.triggerPrice}
                      placeholder="0"
                      onChange={(e) => {
                        const rawValue = e.target.value;
                        const cleanValue = rawValue.replace(/\D/g, "");
                        setFormData((prevState) => ({
                          ...prevState,
                          triggerPrice: cleanValue,
                        }));
                      }}
                      disabled={
                        formData.ordertype === "LIMIT" ||
                        formData.ordertype === "MARKET"
                      }
                      className="input-field"
                    />
                  </div>

                  <div className="input-group">
                    <label className="input-label">Disclosed Qty</label>
                    <input
                      type="text"
                      inputMode="numeric"
                      name="disclosedQty"
                      value={formData?.disclosedQty}
                      placeholder="0"
                      onChange={(e) => {
                        const rawValue = e.target.value;
                        const cleanValue = rawValue.replace(/\D/g, "");
                        setFormData((prevState) => ({
                          ...prevState,
                          disclosedQty: cleanValue,
                        }));
                      }}
                      disabled={formData.ordertype === "SL_MARKET"}
                      className="input-field"
                    />
                  </div>

                  <div className="input-group">
                    <label className="input-label">LTP</label>
                    <div className="ltp-container">
                      <span className="ltp-value">
                        {isLoadingLTP ? "..." : symbolLTP ? symbolLTP : "N/A"}
                      </span>
                      <img
                        src={refreshImg}
                        alt="Refresh LTP"
                        onClick={isLoadingLTP ? null : refreshLTP}
                        className="ltp-refresh"
                        style={{
                          animation: isLoadingLTP
                            ? "spin 1s linear infinite"
                            : "none",
                          opacity: isLoadingLTP ? 0.7 : 1,
                        }}
                        title={isLoadingLTP ? "Loading..." : "Refresh LTP"}
                      />
                    </div>
                  </div>
                </div>
                <div className="duration-section">
                  <div className="radio-group">
                    <label className="radio-label">
                      <input
                        type="radio"
                        name="duration"
                        value="DAY"
                        onChange={handleInputChange}
                      />{" "}
                      Day
                    </label>
                    <label className="radio-label">
                      <input
                        type="radio"
                        name="duration"
                        value="IOC"
                        onChange={handleInputChange}
                      />{" "}
                      IOC
                    </label>
                  </div>

                  <div className="checkbox-container">
                    <label className="checkbox-label">
                      <input
                        type="checkbox"
                        name="amo"
                        onChange={handleInputChange}
                      />
                      AMO
                    </label>
                  </div>
                </div>

                <div className="split-order-container">
                  <h4 className="split-order-title">Split Order</h4>
                  <div className="radio-group">
                    <label className="radio-label">
                      <input type="radio" name="orderType1" value="REGULAR" />{" "}
                      No
                    </label>
                    <label className="radio-label">
                      <input type="radio" name="orderType1" value="BO" /> Auto
                    </label>
                    <label className="radio-label">
                      <input type="radio" name="orderType1" value="CO" /> Qty
                    </label>
                  </div>
                </div>
                <div className="account-selection">
                  <label className="input-label">
                    Accounts: <span style={{ color: "red" }}>*</span>
                  </label>
                  <select
                    onChange={handleAccountChange}
                    value={selectedAccountId}
                    className="account-select"
                    required
                  >
                    <option value="" disabled>
                      -Select Master Account-
                    </option>
                    {data?.map((account, index) => (
                      <React.Fragment key={index}>
                        <option value={account.broker_user_id}>
                          {account.name}: {account.broker_user_id} {"(M)"}
                        </option>
                        {account.child_accounts.map((child, childIndex) => (
                          <option
                            key={`${index}-${childIndex}`}
                            value={child.broker_user_id}
                          >
                            {child.name}: {child.broker_user_id} {"(C)"}
                          </option>
                        ))}
                      </React.Fragment>
                    ))}
                  </select>
                </div>
                <div className="form-actions">
                  <button
                    onClick={resetButton}
                    className="reset-btn"
                    type="button"
                  >
                    Reset
                  </button>
                  <button
                    type="submit"
                    className={`submit-btn ${formData.transactiontype === "SELL" ? "sell" : "buy"
                      }`}
                    disabled={!isFormValid()}
                    title={
                      priceWarning
                        ? "Fix price warning before submitting"
                        : quantityError
                          ? "Fix quantity error before submitting"
                          : !formData.transactiontype
                            ? "Please select an order type"
                            : !formData.exchange
                              ? "Please select an exchange"
                              : !formData.symbol
                                ? "Symbol is required"
                                : !formData.quantity
                                  ? "Quantity is required"
                                  : formData.ordertype === "LIMIT" && !formData.price
                                    ? "Price is required for LIMIT orders"
                                    : (formData.ordertype === "STOP_LOSS" ||
                                      formData.ordertype === "SL_MARKET") &&
                                      !formData.triggerPrice
                                      ? "Trigger price is required"
                                      : !selectedAccountId
                                        ? "Please select an account"
                                        : "Submit order"
                    }
                  >
                    {formData.transactiontype === "SELL" ? "Sell" : "Buy"}
                  </button>
                </div>
              </form>
            </div>
          </Draggable>
          <div className="trade-popup-overlay" onClick={closeTradeModal} />
        </div>
      )}
      {popupOpen && (
        <MasterChild
          open={popupOpen}
          onClose={handleClosePopup}
          selectedItems={mode === "edit" ? selectedItems : []}
          selectedChildAccount={mode === "edit" ? remainingAccounts : []}
          selectedMasterAccount={mode === "edit" ? "" : selectedMasterAccount}
          mode={mode}
          data={data}
        />
      )}
    </div>
  );
}

export default MasterAccount;
